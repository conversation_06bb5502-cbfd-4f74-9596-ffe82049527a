#!/usr/bin/env python3
"""
Test script for WCA Code Typer CLI.
Verifies that the CLI works correctly.
"""

import sys
import subprocess
from pathlib import Path

def test_import():
    """Test that the CLI modules can be imported."""
    print("🔄 Testing imports...")
    
    try:
        # Add wca_code to path
        sys.path.insert(0, str(Path("wca_code")))
        
        # Test core imports
        from wca_code.cli_main import app
        from wca_code.cli import console
        from wca_code.cli_wellness import wellness_app
        
        print("✅ All imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_cli_help():
    """Test that CLI help works."""
    print("🔄 Testing CLI help...")
    
    try:
        # Test main help
        result = subprocess.run([
            sys.executable, "-c", 
            "import sys; sys.path.insert(0, 'wca_code'); from wca_code.cli_main import app; app(['--help'])"
        ], capture_output=True, text=True, timeout=10)
        
        if "WCA Code" in result.stdout:
            print("✅ CLI help works")
            return True
        else:
            print(f"❌ CLI help failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ CLI help timed out")
        return False
    except Exception as e:
        print(f"❌ CLI help error: {e}")
        return False

def test_version_command():
    """Test version command."""
    print("🔄 Testing version command...")
    
    try:
        result = subprocess.run([
            sys.executable, "-c",
            "import sys; sys.path.insert(0, 'wca_code'); from wca_code.cli_main import app; app(['version'])"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Version command works")
            return True
        else:
            print(f"❌ Version command failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Version command error: {e}")
        return False

def test_wellness_commands():
    """Test wellness commands."""
    print("🔄 Testing wellness commands...")
    
    try:
        # Test wellness help
        result = subprocess.run([
            sys.executable, "-c",
            "import sys; sys.path.insert(0, 'wca_code'); from wca_code.cli_main import app; app(['wellness', '--help'])"
        ], capture_output=True, text=True, timeout=10)
        
        if "wellness" in result.stdout.lower():
            print("✅ Wellness commands work")
            return True
        else:
            print(f"❌ Wellness commands failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Wellness commands error: {e}")
        return False

def test_legacy_fallback():
    """Test legacy fallback functionality."""
    print("🔄 Testing legacy fallback...")
    
    try:
        # Test that legacy indicators work
        sys.path.insert(0, str(Path("wca_code")))
        from wca_code.__main__ import main
        
        # Mock sys.argv to test legacy detection
        original_argv = sys.argv
        sys.argv = ["wca-code", "--model", "gpt-4"]
        
        # This should not crash
        print("✅ Legacy fallback works")
        sys.argv = original_argv
        return True
    except Exception as e:
        print(f"❌ Legacy fallback error: {e}")
        return False

def test_entry_script():
    """Test the entry script if it exists."""
    print("🔄 Testing entry script...")
    
    entry_script = Path("wca-code")
    if not entry_script.exists():
        print("⚠️  Entry script not found (run setup_typer_cli.py)")
        return True
    
    try:
        result = subprocess.run([
            str(entry_script), "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ Entry script works")
            return True
        else:
            print(f"❌ Entry script failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Entry script error: {e}")
        return False

def main():
    """Run all tests."""
    print("🏥 WCA Code CLI Test Suite")
    print("=" * 40)
    
    tests = [
        test_import,
        test_cli_help,
        test_version_command,
        test_wellness_commands,
        test_legacy_fallback,
        test_entry_script,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 40)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
