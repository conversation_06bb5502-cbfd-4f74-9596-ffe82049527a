#!/usr/bin/env python3
"""
Find remaining references to 'aider' in wca_code directory.
"""

import os
import re
from pathlib import Path

def find_aider_references(directory):
    """Find all references to 'aider' in Python files."""
    aider_refs = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = Path(root) / file
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        lines = content.split('\n')
                        
                        for line_num, line in enumerate(lines, 1):
                            # Look for various aider references
                            patterns = [
                                r'\baider\b',  # word boundary aider
                                r'"aider',     # quoted aider
                                r"'aider",     # single quoted aider
                                r'aider\.',    # aider.something
                                r'from aider', # import from aider
                                r'import aider', # import aider
                            ]
                            
                            for pattern in patterns:
                                if re.search(pattern, line, re.IGNORECASE):
                                    # Skip comments that are just documentation
                                    if not (line.strip().startswith('#') and 'aider' in line.lower() and ('github' in line.lower() or 'url' in line.lower())):
                                        aider_refs.append({
                                            'file': str(file_path),
                                            'line': line_num,
                                            'content': line.strip(),
                                            'pattern': pattern
                                        })
                                    break
                                    
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    return aider_refs

def main():
    """Main function."""
    wca_code_dir = Path("wca_code")
    
    if not wca_code_dir.exists():
        print("❌ wca_code directory not found")
        return 1
    
    print("🔍 Searching for remaining 'aider' references...")
    
    refs = find_aider_references(wca_code_dir)
    
    if not refs:
        print("✅ No 'aider' references found!")
        return 0
    
    print(f"\n📋 Found {len(refs)} references to 'aider':")
    
    current_file = None
    for ref in refs:
        if ref['file'] != current_file:
            current_file = ref['file']
            print(f"\n📄 {current_file}:")
        
        print(f"  Line {ref['line']:4d}: {ref['content']}")
    
    print(f"\n📊 Total references found: {len(refs)}")
    return 0

if __name__ == "__main__":
    exit(main())
