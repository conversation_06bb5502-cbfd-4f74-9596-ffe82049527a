#!/usr/bin/env python3
"""
Fix critical aider references that prevent wca_code from working independently.
"""

import re
from pathlib import Path

def fix_file(file_path, replacements):
    """Apply replacements to a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed {file_path}")
            return True
        else:
            print(f"⚪ No changes needed in {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return False

def main():
    """Fix critical references."""
    wca_code_dir = Path("wca_code")
    
    # Critical fixes for imports and resource references
    critical_fixes = [
        # models.py - fix resource import
        (
            wca_code_dir / "models.py",
            [
                (r'importlib\.resources\.open_text\("aider\.resources"', 
                 r'importlib.resources.open_text("wca_code.resources"'),
            ]
        ),
        
        # help.py - fix resource imports
        (
            wca_code_dir / "help.py",
            [
                (r'importlib_resources\.files\("aider\.website"\)', 
                 r'importlib_resources.files("wca_code.website")'),
            ]
        ),
        
        # resources/__init__.py - fix comment
        (
            wca_code_dir / "resources" / "__init__.py",
            [
                (r'importlib_resources\.files\("aider\.resources"\)', 
                 r'importlib_resources.files("wca_code.resources")'),
            ]
        ),
        
        # versioncheck.py - fix import
        (
            wca_code_dir / "versioncheck.py",
            [
                (r'from \. import __init__ as aider', 
                 r'from . import __init__ as wca_code_module'),
                (r'aider\.__version__', 
                 r'wca_code_module.__version__'),
            ]
        ),
        
        # Fix config file names to be wca_code specific
        (
            wca_code_dir / "cli.py",
            [
                (r'\.aider\.conf\.yml', r'.wca_code.conf.yml'),
            ]
        ),
        
        (
            wca_code_dir / "cli_simple.py",
            [
                (r'\.aider\.conf\.yml', r'.wca_code.conf.yml'),
            ]
        ),
        
        (
            wca_code_dir / "main.py",
            [
                (r'\.aider\.conf\.yml', r'.wca_code.conf.yml'),
                (r'\.aider\.model\.settings\.yml', r'.wca_code.model.settings.yml'),
                (r'\.aider\.model\.metadata\.json', r'.wca_code.model.metadata.json'),
                (r'\.aider\.input\.history', r'.wca_code.input.history'),
                (r'\.aider\.chat\.history\.md', r'.wca_code.chat.history.md'),
                (r'\.aider\.llm\.history', r'.wca_code.llm.history'),
            ]
        ),
        
        (
            wca_code_dir / "args.py",
            [
                (r'\.aider\.model\.settings\.yml', r'.wca_code.model.settings.yml'),
                (r'\.aider\.model\.metadata\.json', r'.wca_code.model.metadata.json'),
                (r'\.aider\.input\.history', r'.wca_code.input.history'),
                (r'\.aider\.chat\.history\.md', r'.wca_code.chat.history.md'),
                (r'\.aider\.llm\.history', r'.wca_code.llm.history'),
                (r'\.aider\.conf\.yml', r'.wca_code.conf.yml'),
                (r'auto_env_var_prefix="AIDER_"', r'auto_env_var_prefix="WCA_CODE_"'),
            ]
        ),
        
        # Fix cache directories
        (
            wca_code_dir / "analytics.py",
            [
                (r'Path\.home\(\) / "\.aider"', r'Path.home() / ".wca_code"'),
            ]
        ),
        
        (
            wca_code_dir / "models.py",
            [
                (r'Path\.home\(\) / "\.aider"', r'Path.home() / ".wca_code"'),
            ]
        ),
        
        (
            wca_code_dir / "openrouter.py",
            [
                (r'Path\.home\(\) / "\.aider"', r'Path.home() / ".wca_code"'),
            ]
        ),
        
        (
            wca_code_dir / "onboarding.py",
            [
                (r'~\/\.aider', r'~/.wca_code'),
                (r'Path\.home\(\) / "\.aider"', r'Path.home() / ".wca_code"'),
            ]
        ),
        
        (
            wca_code_dir / "help.py",
            [
                (r'Path\.home\(\) / "\.aider"', r'Path.home() / ".wca_code"'),
            ]
        ),
        
        (
            wca_code_dir / "versioncheck.py",
            [
                (r'Path\.home\(\) / "\.aider"', r'Path.home() / ".wca_code"'),
            ]
        ),
        
        (
            wca_code_dir / "repomap.py",
            [
                (r'\.aider\.tags\.cache', r'.wca_code.tags.cache'),
            ]
        ),
        
        (
            wca_code_dir / "watch.py",
            [
                (r'\.aider\*', r'.wca_code*'),
            ]
        ),
        
        # Fix gitignore patterns
        (
            wca_code_dir / "main.py",
            [
                (r'\.aider\*', r'.wca_code*'),
                (r'repo\.ignored\("\.aider"\)', r'repo.ignored(".wca_code")'),
            ]
        ),
        
        (
            wca_code_dir / "gui.py",
            [
                (r'\.aider\*', r'.wca_code*'),
            ]
        ),
    ]
    
    fixed_count = 0
    total_count = len(critical_fixes)
    
    for file_path, replacements in critical_fixes:
        if file_path.exists():
            if fix_file(file_path, replacements):
                fixed_count += 1
        else:
            print(f"⚠️  File not found: {file_path}")
    
    print(f"\n📊 Summary:")
    print(f"   Files processed: {total_count}")
    print(f"   Files fixed: {fixed_count}")
    
    if fixed_count > 0:
        print("\n🎉 Critical references fixed!")
    else:
        print("\n✨ All critical references were already correct!")
    
    return 0

if __name__ == "__main__":
    exit(main())
