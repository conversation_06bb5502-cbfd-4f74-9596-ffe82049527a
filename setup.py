from setuptools import setup, find_packages

setup(
    name="wca_code",
    version="0.0.1",
    packages=find_packages(),
    install_requires=[
        "gitpython",
        "importlib_resources",
        "python-dotenv",
        "shtab",
        "prompt_toolkit",
        "httpx",
        "litellm",
        "networkx",
        "numpy",
        "pygments",
        "rich",
        "babel",
        "jsonschema",
    ],
    entry_points={
        "console_scripts": [
            "wca-code=wca_code.wca_code:main",
        ],
    },
    package_data={
        "wca_code": [
            "queries/**/*",
            "resources/**/*",
        ],
    },
    include_package_data=True,
)
