#!/usr/bin/env python3
"""
Setup script for WCA Code Typer CLI.
Installs required dependencies and sets up the CLI.
"""

import subprocess
import sys
from pathlib import Path

def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} is compatible")
    return True

def install_typer_dependencies():
    """Install Typer and Rich dependencies."""
    dependencies = [
        "typer[all]>=0.9.0",
        "rich>=13.0.0",
        "click>=8.0.0",
    ]
    
    for dep in dependencies:
        if not run_command(f"{sys.executable} -m pip install '{dep}'", f"Installing {dep}"):
            return False
    return True

def create_entry_script():
    """Create a convenient entry script."""
    script_content = '''#!/usr/bin/env python3
"""
WCA Code CLI entry script.
"""

import sys
from pathlib import Path

# Add the wca_code directory to Python path
wca_code_dir = Path(__file__).parent / "wca_code"
sys.path.insert(0, str(wca_code_dir))

from wca_code.cli_main import app

if __name__ == "__main__":
    app()
'''
    
    script_path = Path("wca-code")
    script_path.write_text(script_content)
    script_path.chmod(0o755)
    print(f"✅ Created entry script: {script_path}")
    return True

def setup_shell_completion():
    """Set up shell completion."""
    print("🔄 Setting up shell completion...")
    
    # Create completion script
    completion_script = '''#!/bin/bash
# WCA Code shell completion setup
# Add this to your ~/.bashrc or ~/.zshrc:
# source /path/to/this/script

_wca_code_completion() {
    local IFS=$'\\n'
    COMPREPLY=( $(env COMP_WORDS="${COMP_WORDS[*]}" \\
                     COMP_CWORD=$COMP_CWORD \\
                     _WCA_CODE_COMPLETE=complete $1) )
}

complete -F _wca_code_completion -o default wca-code
'''
    
    completion_path = Path("wca-code-completion.sh")
    completion_path.write_text(completion_script)
    print(f"✅ Created completion script: {completion_path}")
    print("   Add 'source wca-code-completion.sh' to your shell config")
    return True

def test_installation():
    """Test the installation."""
    print("🔄 Testing installation...")
    
    try:
        # Test import
        sys.path.insert(0, str(Path("wca_code")))
        from wca_code.cli_main import app
        print("✅ CLI imports successfully")
        
        # Test basic functionality
        print("✅ Installation test passed")
        return True
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        return False

def main():
    """Main setup function."""
    print("🏥 WCA Code Typer CLI Setup")
    print("=" * 40)
    
    # Check prerequisites
    if not check_python_version():
        return 1
    
    # Install dependencies
    if not install_typer_dependencies():
        print("❌ Failed to install dependencies")
        return 1
    
    # Create entry script
    if not create_entry_script():
        print("❌ Failed to create entry script")
        return 1
    
    # Setup shell completion
    setup_shell_completion()
    
    # Test installation
    if not test_installation():
        print("❌ Installation test failed")
        return 1
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Run: ./wca-code --help")
    print("2. Try: ./wca-code chat")
    print("3. Check wellness: ./wca-code wellness check")
    print("4. Add shell completion: source wca-code-completion.sh")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
