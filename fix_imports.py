#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to fix all imports in wca_code to be self-contained.
Replaces all 'from aider' imports with relative imports.
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path):
    """Fix imports in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern to match 'from aider' imports
        patterns = [
            # from aider.module import something
            (r'from aider\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*) import', r'from .\1 import'),
            # from aider import something
            (r'from aider import', r'from . import'),
            # import aider.module
            (r'import aider\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)', r'from . import \1'),
            # import aider
            (r'^import aider$', r'from . import __init__ as aider'),
        ]
        
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Special case: fix specific problematic imports
        special_fixes = [
            # Fix dump imports
            (r'from aider\.dump import dump', r'from .dump import dump'),
            # Fix version imports
            (r'import aider$', r'from . import __init__ as aider'),
            (r'aider\.__version__', r'aider.__version__'),
        ]
        
        for pattern, replacement in special_fixes:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Write back if changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed imports in {file_path}")
            return True
        else:
            print(f"⚪ No changes needed in {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error processing {file_path}: {e}")
        return False

def main():
    """Main function to fix all imports in wca_code directory."""
    wca_code_dir = Path("wca_code")
    
    if not wca_code_dir.exists():
        print("❌ wca_code directory not found")
        return 1
    
    print("🔧 Fixing imports in wca_code directory...")
    
    # Find all Python files
    python_files = list(wca_code_dir.rglob("*.py"))
    
    fixed_count = 0
    total_count = len(python_files)
    
    for py_file in python_files:
        if fix_imports_in_file(py_file):
            fixed_count += 1
    
    print(f"\n📊 Summary:")
    print(f"   Total files: {total_count}")
    print(f"   Files fixed: {fixed_count}")
    print(f"   Files unchanged: {total_count - fixed_count}")
    
    if fixed_count > 0:
        print("\n🎉 Import fixing completed!")
    else:
        print("\n✨ All imports were already correct!")
    
    return 0

if __name__ == "__main__":
    exit(main())
