# 🏥 WCA Code - Modern Typer CLI

A modern, user-friendly CLI for WCA Code (Code Wellness Coach) built with <PERSON><PERSON> and Rich for beautiful terminal interfaces.

## 🚀 Quick Start

### Installation

1. **Install dependencies:**
   ```bash
   python setup_typer_cli.py
   ```

2. **Run the CLI:**
   ```bash
   ./wca-code --help
   ```

### Basic Usage

```bash
# Start an AI coding session
./wca-code chat

# Check code wellness
./wca-code wellness check

# Lint and fix code
./wca-code lint

# Generate a commit
./wca-code commit

# Show version
./wca-code version
```

## 📋 Commands Overview

### Core Commands

| Command | Description | Example |
|---------|-------------|---------|
| `chat` | Start interactive AI coding session | `wca-code chat file.py` |
| `lint` | Lint and fix code issues | `wca-code lint --fix` |
| `test` | Run tests and fix issues | `wca-code test --test-cmd "pytest"` |
| `commit` | Generate smart commits | `wca-code commit` |
| `watch` | Watch files for AI comments | `wca-code watch` |
| `models` | List available AI models | `wca-code models gpt` |
| `config` | Manage configuration | `wca-code config --init` |

### Wellness Commands

| Command | Description | Example |
|---------|-------------|---------|
| `wellness check` | Comprehensive health check | `wca-code wellness check --detailed` |
| `wellness score` | Calculate wellness score | `wca-code wellness score` |
| `wellness trends` | Show wellness trends | `wca-code wellness trends --days 30` |
| `wellness fix` | Auto-fix common issues | `wca-code wellness fix --auto` |

### Git Commands

| Command | Description | Example |
|---------|-------------|---------|
| `git status` | Git status with AI insights | `wca-code git status` |
| `git diff` | Git diff with analysis | `wca-code git diff file.py` |

## 🎨 Features

### Beautiful Terminal UI
- **Rich formatting** with colors and emojis
- **Progress bars** for long operations
- **Tables** for structured data
- **Panels** for important information

### Smart Command Detection
- **Auto-fallback** to legacy mode for compatibility
- **Intelligent argument parsing**
- **Context-aware help**

### Wellness Focus
- **Code health scoring**
- **Automated issue detection**
- **Trend analysis**
- **Smart recommendations**

## 🔧 Configuration

### Initialize Configuration
```bash
wca-code config --init
```

This creates a `.aider.conf.yml` file with common settings:

```yaml
# WCA Code Configuration
model: gpt-4
auto-commits: true
dark-mode: false
stream: true
pretty: true
```

### Show Current Config
```bash
wca-code config --show
```

### Edit Configuration
```bash
wca-code config --edit
```

## 📊 Wellness Features

### Health Check
```bash
wca-code wellness check
```

Analyzes:
- Git repository health
- Code quality metrics
- Dependency status
- Test coverage

### Wellness Score
```bash
wca-code wellness score
```

Provides a 0-10 score across:
- **Git Health** (20%): Commit frequency, repo status
- **Code Quality** (30%): Linting, complexity
- **Dependencies** (20%): Security, updates
- **Test Coverage** (30%): Coverage percentage

### Auto-Fix Issues
```bash
wca-code wellness fix --auto
```

Automatically fixes:
- Git configuration issues
- Code formatting problems
- Dependency vulnerabilities
- Missing test files

## 🎯 Advanced Usage

### Chat with Specific Model
```bash
wca-code chat --model gpt-4 --message "Refactor this function"
```

### Lint with Custom Rules
```bash
wca-code lint --fix src/
```

### Watch Mode
```bash
wca-code watch --model gpt-3.5-turbo
```

### Dry Run Mode
```bash
wca-code commit --dry-run
```

## 🔄 Legacy Compatibility

The CLI maintains full compatibility with the original argparse interface:

```bash
# Force legacy mode
wca-code --legacy --model gpt-4 --message "Hello"

# Legacy arguments auto-detected
wca-code --model gpt-4 file.py
```

## 🐛 Troubleshooting

### Run Diagnostics
```bash
wca-code doctor
```

This checks:
- Python version compatibility
- Git availability
- Dependency status
- Repository health

### Common Issues

**Import Errors:**
```bash
python setup_typer_cli.py
```

**Permission Issues:**
```bash
chmod +x wca-code
```

**Shell Completion:**
```bash
source wca-code-completion.sh
```

## 🎨 Customization

### Color Themes
```bash
# Dark mode
wca-code chat --dark-mode

# Light mode  
wca-code chat --light-mode
```

### Verbose Output
```bash
wca-code chat --verbose
```

### Custom Test Commands
```bash
wca-code test --test-cmd "python -m pytest -v"
```

## 📈 Roadmap

- [ ] **Interactive wellness dashboard**
- [ ] **Code quality trends visualization**
- [ ] **AI-powered refactoring suggestions**
- [ ] **Team wellness reports**
- [ ] **Integration with CI/CD pipelines**
- [ ] **Custom wellness rules**

## 🤝 Contributing

1. **Add new commands** in `wca_code/cli.py`
2. **Extend wellness features** in `wca_code/cli_wellness.py`
3. **Update main app** in `wca_code/cli_main.py`
4. **Test thoroughly** with both modes

## 📝 License

Same as the original Aider project - Apache 2.0 License.

---

**Happy Coding! 🏥✨**
