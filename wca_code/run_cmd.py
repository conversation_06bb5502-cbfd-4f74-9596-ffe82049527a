"""
Provides command execution functionality through an object-oriented design.
"""
from abc import ABC, abstractmethod
import os
import platform
import subprocess
import sys
from io import BytesIO

import pexpect
import psutil


class CommandRunner(ABC):
    """Abstract base class for command execution."""

    def __init__(self, verbose=False, cwd=None, encoding=sys.stdout.encoding):
        self.verbose = verbose
        self.cwd = cwd
        self.encoding = encoding

    @abstractmethod
    def run(self, command: str) -> tuple[int, str]:
        """Run a command and return exit status and output."""
        pass

    def _log(self, message: str) -> None:
        """Log message if verbose mode is enabled."""
        if self.verbose:
            print(message)


class PexpectRunner(CommandRunner):
    """Command runner that uses pexpect for interactive commands."""

    def run(self, command: str) -> tuple[int, str]:
        self._log(f"Using PexpectRunner: {command}")

        output = BytesIO()

        def output_callback(b):
            output.write(b)
            return b

        try:
            # Use the SHELL environment variable, falling back to /bin/sh
            shell = os.environ.get("SHELL", "/bin/sh")
            self._log(f"With shell: {shell}")

            if os.path.exists(shell):
                # Use the shell from SHELL environment variable
                self._log(f"Running pexpect.spawn with shell: {shell}")
                child = pexpect.spawn(
                    shell,
                    args=["-i", "-c", command],
                    encoding="utf-8",
                    cwd=self.cwd
                )
            else:
                # Fall back to spawning the command directly
                self._log("Running pexpect.spawn without shell.")
                child = pexpect.spawn(command, encoding="utf-8", cwd=self.cwd)

            # Transfer control to the user, capturing output
            child.interact(output_filter=output_callback)

            # Wait for the command to finish and get the exit status
            child.close()
            return child.exitstatus, output.getvalue().decode("utf-8", errors="replace")

        except (pexpect.ExceptionPexpect, TypeError, ValueError) as e:
            error_msg = f"Error running command {command}: {e}"
            return 1, error_msg


class SubprocessRunner(CommandRunner):
    """Command runner that uses subprocess for non-interactive commands."""

    def _get_windows_parent_process_name(self) -> str | None:
        """Get the name of the parent process on Windows."""
        try:
            current_process = psutil.Process()
            while True:
                parent = current_process.parent()
                if parent is None:
                    break
                parent_name = parent.name().lower()
                if parent_name in ["powershell.exe", "cmd.exe"]:
                    return parent_name
                current_process = parent
            return None
        except Exception:
            return None

    def run(self, command: str) -> tuple[int, str]:
        self._log(f"Using SubprocessRunner: {command}")

        try:
            shell = os.environ.get("SHELL", "/bin/sh")
            parent_process = None

            # Handle Windows shell specifics
            if platform.system() == "Windows":
                parent_process = self._get_windows_parent_process_name()
                if parent_process == "powershell.exe":
                    command = f"powershell -Command {command}"

            if self.verbose:
                print("Running command:", command)
                print("SHELL:", shell)
                if platform.system() == "Windows":
                    print("Parent process:", parent_process)

            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                shell=True,
                encoding=self.encoding,
                errors="replace",
                bufsize=0,  # Set bufsize to 0 for unbuffered output
                universal_newlines=True,
                cwd=self.cwd,
            )

            output = []
            while True:
                chunk = process.stdout.read(1)
                if not chunk:
                    break
                print(chunk, end="", flush=True)  # Print the chunk in real-time
                output.append(chunk)  # Store the chunk for later use

            process.wait()
            return process.returncode, "".join(output)

        except Exception as e:
            return 1, str(e)


def create_runner(verbose=False, error_print=None, cwd=None) -> CommandRunner:
    """Factory function to create the appropriate CommandRunner instance."""
    if sys.stdin.isatty() and hasattr(pexpect, "spawn") and platform.system() != "Windows":
        return PexpectRunner(verbose=verbose, cwd=cwd)
    return SubprocessRunner(verbose=verbose, cwd=cwd)


def run_cmd(command: str, verbose=False, error_print=None, cwd=None) -> tuple[int, str]:
    """
    Run a shell command and return its exit status and output.
    This function maintains backward compatibility with the original interface.
    """
    try:
        runner = create_runner(verbose, error_print, cwd)
        return runner.run(command)


    except OSError as e:
        error_message = f"Error occurred while running command '{command}': {str(e)}"
        if error_print is None:
            print(error_message)
        else:
            error_print(error_message)
        return 1, error_message
