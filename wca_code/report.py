import os
import platform
import subprocess
import sys
import traceback
import urllib.parse
import webbrowser
from dataclasses import dataclass
from typing import Optional

from . import __version__
from .urls import github_issues
from .versioncheck import VERSION_CHECK_FNAME

FENCE = "`" * 3


@dataclass
class SystemInfo:
    """Class to encapsulate system information."""
    python_implementation: str
    python_version: str
    platform_info: str
    is_virtual_env: bool
    os_system: str
    os_release: str
    architecture: str
    git_version: Optional[str] = None

    @classmethod
    def collect(cls) -> 'SystemInfo':
        """Collect all system information."""
        return cls(
            python_implementation=platform.python_implementation(),
            python_version=sys.version.split()[0],
            platform_info=platform.platform(),
            is_virtual_env=sys.prefix != sys.base_prefix,
            os_system=platform.system(),
            os_release=platform.release(),
            architecture=platform.architecture()[0],
            git_version=cls._get_git_version()
        )

    @staticmethod
    def _get_git_version() -> Optional[str]:
        """Get git version information."""
        try:
            return subprocess.check_output(["git", "--version"]).decode().strip()
        except Exception:
            return None

    def format_report(self) -> str:
        """Format system information into a report string."""
        parts = [
            f"Aider version: {__version__}",
            f"Python version: {self.python_version}",
            f"Platform: {self.platform_info}",
            f"Python implementation: {self.python_implementation}",
            f"Virtual environment: {'Yes' if self.is_virtual_env else 'No'}",
            f"OS: {self.os_system} {self.os_release} ({self.architecture})",
        ]
        
        if self.git_version:
            parts.append(f"Git version: {self.git_version}")
        else:
            parts.append("Git information unavailable")
            
        return "\n".join(parts)


class GitHubIssueReporter:
    """Class to handle GitHub issue reporting."""
    
    def __init__(self, system_info: SystemInfo):
        self.system_info = system_info

    def _build_issue_url(self, issue_text: str, title: str | None = None) -> tuple[str, str]:
        """Build the GitHub issue URL with the given text and title."""
        full_issue_text = f"{self.system_info.format_report()}\n\n{issue_text}"
        title = title or "Bug report"
        
        params = {
            "body": full_issue_text,
            "title": title
        }
        return title, f"{github_issues}?{urllib.parse.urlencode(params)}"

    def _confirm_submission(self, title: str, issue_text: str) -> bool:
        """Ask for user confirmation to submit the issue."""
        print(f"\n# {title}\n")
        print(issue_text.strip())
        print()
        print("Please consider reporting this bug to help improve the code wellness coach!")
        prompt = "Open a GitHub Issue pre-filled with the above error in your browser? (Y/n) "
        confirmation = input(prompt).strip().lower()
        return not confirmation or confirmation.startswith("y")

    def report(self, issue_text: str, title: Optional[str] = None, confirm: bool = True) -> None:
        """Report an issue to GitHub."""
        title, issue_url = self._build_issue_url(issue_text, title)

        if confirm and not self._confirm_submission(title, issue_text):
            return

        print("Attempting to open the issue URL in your default web browser...")
        try:
            if webbrowser.open(issue_url):
                print("Browser window should be opened.")
        except Exception:
            pass

        if confirm:
            print("\n\nYou can also use this URL to file the GitHub Issue:\n")
            print(issue_url)
            print("\n")


class ExceptionHandler:
    """Class to handle uncaught exceptions."""

    def __init__(self, reporter: GitHubIssueReporter):
        self.reporter = reporter

    def _clean_traceback(self, tb_lines: list[str]) -> str:
        """Clean the traceback by replacing full paths with basenames."""
        cleaned_lines = []
        for line in tb_lines:
            try:
                if "File " in line:
                    parts = line.split('"')
                    if len(parts) > 1:
                        full_path = parts[1]
                        basename = os.path.basename(full_path)
                        line = line.replace(full_path, basename)
            except Exception:
                pass
            cleaned_lines.append(line)
        return "".join(cleaned_lines)

    def _get_exception_details(self, exc_traceback) -> tuple[str, int, str]:
        """Get details about the innermost frame of the exception."""
        innermost_tb = exc_traceback
        while innermost_tb.tb_next:
            innermost_tb = innermost_tb.tb_next

        filename = innermost_tb.tb_frame.f_code.co_filename
        line_number = innermost_tb.tb_lineno
        try:
            basename = os.path.basename(filename)
        except Exception:
            basename = filename

        return basename, line_number, filename

    def handle(self, exc_type, exc_value, exc_traceback):
        """Handle an uncaught exception."""
        if issubclass(exc_type, KeyboardInterrupt):
            return sys.__excepthook__(exc_type, exc_value, exc_traceback)

        sys.excepthook = None

        try:
            if VERSION_CHECK_FNAME.exists():
                VERSION_CHECK_FNAME.unlink()
        except Exception:
            pass

        tb_lines = traceback.format_exception(exc_type, exc_value, exc_traceback)
        tb_text = self._clean_traceback(tb_lines)

        basename, line_number, _ = self._get_exception_details(exc_traceback)
        exception_type = exc_type.__name__

        issue_text = f"An uncaught exception occurred:\n\n{FENCE}\n{tb_text}\n{FENCE}"
        title = f"Uncaught {exception_type} in {basename} line {line_number}"

        self.reporter.report(issue_text, title=title)

        sys.__excepthook__(exc_type, exc_value, exc_traceback)

    # Call the default exception handler
    sys.__excepthook__(exc_type, exc_value, exc_traceback)


def setup_exception_handler():
    """Set up the global exception handler."""
    system_info = SystemInfo.collect()
    reporter = GitHubIssueReporter(system_info)
    handler = ExceptionHandler(reporter)
    sys.excepthook = handler.handle


class ReportCLI:
    """Command Line Interface for the reporting system."""

    def __init__(self):
        self.system_info = SystemInfo.collect()
        self.reporter = GitHubIssueReporter(self.system_info)

    def _get_input(self) -> tuple[Optional[str], str]:
        """Get issue title and text from user input."""
        print("Enter the issue title (optional, press Enter to skip):")
        title = input().strip()
        if not title:
            title = None
            
        print("Enter the issue text (Ctrl+D to finish):")
        issue_text = sys.stdin.read().strip()
        return title, issue_text

    def _parse_args(self) -> tuple[Optional[str], str]:
        """Parse command line arguments."""
        if len(sys.argv) > 2:
            return sys.argv[1], sys.argv[2]
        elif len(sys.argv) > 1:
            return None, sys.argv[1]
        else:
            return self._get_input()

    def run(self):
        """Run the reporting CLI."""
        title, issue_text = self._parse_args()
        self.reporter.report(issue_text, title)


def main():
    """Main entry point for the reporting system."""
    setup_exception_handler()
    cli = ReportCLI()
    cli.run()


if __name__ == "__main__":
    main()


if __name__ == "__main__":
    main()
