"""
Modern Typer-based CLI for WCA Code.
A code wellness coach that helps you keep your codebase healthy.
"""

import os
import sys
from pathlib import Path
from typing import List, Optional, Annotated
import typer
from rich.console import Console
# from rich.table import Table  # Will be used later
from rich import print as rprint

# Import the existing main functionality
try:
    from .main import main as legacy_main
    from . import __version__
except ImportError:
    # Handle case when running as script
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent))
    from main import main as legacy_main
    import __init__ as version_module
    __version__ = getattr(version_module, '__version__', '0.1.0')

# Create the main Typer app
app = typer.Typer(
    name="wca-code",
    help="🏥 WCA Code - Your AI Code Wellness Coach",
    add_completion=False,
    rich_markup_mode="rich",
    no_args_is_help=True,
)

console = Console()

# Global options that apply to most commands
CommonOptions = Annotated[
    Optional[str],
    typer.Option(help="Specify the model to use for the main chat")
]

VerboseOption = Annotated[
    bool,
    typer.Option("--verbose", "-v", help="Enable verbose output")
]

DryRunOption = Annotated[
    bool,
    typer.Option("--dry-run", help="Perform a dry run without modifying files")
]


@app.command()
def version():
    """Show version information."""
    rprint(f"[bold blue]WCA Code[/bold blue] version [green]{__version__}[/green]")


@app.command()
def models(
    pattern: Annotated[Optional[str], typer.Argument(help="Pattern to filter models")] = None,
    verbose: VerboseOption = False,
):
    """List available AI models."""
    # Import here to avoid circular imports
    from . import models as model_module
    from .io import InputOutput

    io = InputOutput(pretty=True, verbose=verbose)

    if pattern:
        model_module.print_matching_models(io, pattern)
    else:
        # Show all models in a nice table
        console.print("\n[bold]Available Models:[/bold]")
        # This would need to be implemented to show available models
        console.print("Use --pattern to filter models")


@app.command()
def chat(
    files: Annotated[List[Path], typer.Argument(help="Files to edit with AI")] = None,
    model: CommonOptions = None,
    message: Annotated[Optional[str], typer.Option("--message", "-m", help="Single message to send")] = None,
    verbose: VerboseOption = False,
    dry_run: DryRunOption = False,
    auto_commits: Annotated[bool, typer.Option(help="Enable auto-commits")] = True,
    stream: Annotated[bool, typer.Option(help="Enable streaming responses")] = True,
    dark_mode: Annotated[bool, typer.Option(help="Use dark mode colors")] = False,
    light_mode: Annotated[bool, typer.Option(help="Use light mode colors")] = False,
):
    """Start an interactive AI coding session."""

    # Convert Typer arguments to legacy format
    argv = []

    # Add files
    if files:
        argv.extend([str(f) for f in files])

    # Add options
    if model:
        argv.extend(["--model", model])
    if message:
        argv.extend(["--message", message])
    if verbose:
        argv.append("--verbose")
    if dry_run:
        argv.append("--dry-run")
    if not auto_commits:
        argv.append("--no-auto-commits")
    if not stream:
        argv.append("--no-stream")
    if dark_mode:
        argv.append("--dark-mode")
    if light_mode:
        argv.append("--light-mode")

    # Call the legacy main function
    try:
        return legacy_main(argv)
    except KeyboardInterrupt:
        console.print("\n[yellow]Session interrupted by user[/yellow]")
        return 0
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        return 1


@app.command()
def lint(
    files: Annotated[List[Path], typer.Argument(help="Files to lint")] = None,
    fix: Annotated[bool, typer.Option(help="Automatically fix issues")] = False,
    verbose: VerboseOption = False,
):
    """Lint and optionally fix code issues."""

    argv = ["--lint"]

    if files:
        argv.extend([str(f) for f in files])
    if fix:
        # Add fix-related arguments if needed
        pass
    if verbose:
        argv.append("--verbose")

    try:
        return legacy_main(argv)
    except Exception as e:
        console.print(f"[red]Linting failed: {e}[/red]")
        return 1


@app.command()
def test(
    test_cmd: Annotated[Optional[str], typer.Option(help="Command to run tests")] = None,
    verbose: VerboseOption = False,
):
    """Run tests and fix any issues found."""

    argv = ["--test"]

    if test_cmd:
        argv.extend(["--test-cmd", test_cmd])
    if verbose:
        argv.append("--verbose")

    try:
        return legacy_main(argv)
    except Exception as e:
        console.print(f"[red]Testing failed: {e}[/red]")
        return 1


@app.command()
def commit(
    message: Annotated[Optional[str], typer.Option(help="Custom commit message prompt")] = None,
    verbose: VerboseOption = False,
    dry_run: DryRunOption = False,
):
    """Generate and create a commit for pending changes."""

    argv = ["--commit"]

    if message:
        argv.extend(["--commit-prompt", message])
    if verbose:
        argv.append("--verbose")
    if dry_run:
        argv.append("--dry-run")

    try:
        return legacy_main(argv)
    except Exception as e:
        console.print(f"[red]Commit failed: {e}[/red]")
        return 1


@app.command()
def watch(
    files: Annotated[List[Path], typer.Argument(help="Files to watch")] = None,
    model: CommonOptions = None,
    verbose: VerboseOption = False,
):
    """Watch files for AI coding comments and auto-respond."""

    argv = ["--watch-files"]

    if files:
        argv.extend([str(f) for f in files])
    if model:
        argv.extend(["--model", model])
    if verbose:
        argv.append("--verbose")

    console.print("[blue]Starting file watcher...[/blue]")
    console.print("[dim]Press Ctrl+C to stop[/dim]")

    try:
        return legacy_main(argv)
    except KeyboardInterrupt:
        console.print("\n[yellow]File watcher stopped[/yellow]")
        return 0
    except Exception as e:
        console.print(f"[red]Watch failed: {e}[/red]")
        return 1


@app.command()
def repo_map(
    verbose: VerboseOption = False,
):
    """Show the repository map."""

    argv = ["--show-repo-map"]

    if verbose:
        argv.append("--verbose")

    try:
        return legacy_main(argv)
    except Exception as e:
        console.print(f"[red]Failed to show repo map: {e}[/red]")
        return 1


@app.command()
def config(
    show: Annotated[bool, typer.Option("--show", help="Show current configuration")] = False,
    edit: Annotated[bool, typer.Option("--edit", help="Edit configuration file")] = False,
    init: Annotated[bool, typer.Option("--init", help="Initialize configuration")] = False,
):
    """Manage WCA Code configuration."""

    if show:
        # Show current config
        config_files = [
            Path.home() / ".aider.conf.yml",
            Path.cwd() / ".aider.conf.yml",
        ]

        console.print("[bold]Configuration Files:[/bold]")
        for config_file in config_files:
            status = "[green]exists[/green]" if config_file.exists() else "[dim]not found[/dim]"
            console.print(f"  {config_file} - {status}")

    elif edit:
        # Open config file in editor
        config_file = Path.cwd() / ".aider.conf.yml"
        if not config_file.exists():
            console.print(f"[yellow]Creating new config file: {config_file}[/yellow]")
            config_file.write_text("# WCA Code Configuration\n")

        import subprocess
        editor = os.environ.get("EDITOR", "nano")
        subprocess.run([editor, str(config_file)])

    elif init:
        # Initialize a new config file
        config_file = Path.cwd() / ".aider.conf.yml"
        if config_file.exists():
            if not typer.confirm(f"Config file {config_file} already exists. Overwrite?"):
                return

        sample_config = """# WCA Code Configuration
# See https://aider.chat/docs/config.html for all options

# Model settings
model: gpt-4
# weak-model: gpt-3.5-turbo

# Git settings
auto-commits: true
dirty-commits: true

# Output settings
dark-mode: false
stream: true
pretty: true

# Advanced settings
# map-tokens: 1024
# cache-prompts: false
"""
        config_file.write_text(sample_config)
        console.print(f"[green]Created config file: {config_file}[/green]")

    else:
        console.print("Use --show, --edit, or --init")


# Create subcommands for better organization
git_app = typer.Typer(name="git", help="Git-related commands")
app.add_typer(git_app, name="git")

@git_app.command("status")
def git_status():
    """Show git status with AI insights."""
    console.print("[blue]Git Status with AI Insights[/blue]")
    # This could be enhanced to show git status and suggest actions
    import subprocess
    result = subprocess.run(["git", "status", "--porcelain"], capture_output=True, text=True)
    if result.stdout:
        console.print("[yellow]Modified files:[/yellow]")
        for line in result.stdout.strip().split('\n'):
            console.print(f"  {line}")
    else:
        console.print("[green]Working directory clean[/green]")


@git_app.command("diff")
def git_diff(
    files: Annotated[List[Path], typer.Argument(help="Files to diff")] = None,
):
    """Show git diff with AI analysis."""
    cmd = ["git", "diff"]
    if files:
        cmd.extend([str(f) for f in files])

    import subprocess
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.stdout:
        console.print(result.stdout)
    else:
        console.print("[dim]No changes to show[/dim]")


if __name__ == "__main__":
    app()
