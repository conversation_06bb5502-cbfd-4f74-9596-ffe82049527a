#!/usr/bin/env python3
"""
Simple CLI for WCA Code that works without external dependencies.
This is a fallback when Typer/Rich are not available.
"""

import sys
import argparse
from pathlib import Path

def create_simple_parser():
    """Create a simple argument parser."""
    parser = argparse.ArgumentParser(
        prog="wca-code",
        description="🏥 WCA Code - Your AI Code Wellness Coach",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  wca-code chat file.py          Start AI coding session
  wca-code wellness-check        Check code health
  wca-code lint                  Lint code
  wca-code commit                Generate commit
  wca-code version               Show version
        """
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Chat command
    chat_parser = subparsers.add_parser("chat", help="Start AI coding session")
    chat_parser.add_argument("files", nargs="*", help="Files to edit")
    chat_parser.add_argument("--model", help="AI model to use")
    chat_parser.add_argument("-m", "--message", help="Single message to send")
    chat_parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    chat_parser.add_argument("--dry-run", action="store_true", help="Dry run mode")

    # Wellness command
    wellness_parser = subparsers.add_parser("wellness-check", help="Check code wellness")
    wellness_parser.add_argument("--detailed", action="store_true", help="Detailed analysis")
    wellness_parser.add_argument("path", nargs="?", default=".", help="Path to check")

    # Lint command
    lint_parser = subparsers.add_parser("lint", help="Lint code")
    lint_parser.add_argument("files", nargs="*", help="Files to lint")
    lint_parser.add_argument("--fix", action="store_true", help="Auto-fix issues")
    lint_parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")

    # Test command
    test_parser = subparsers.add_parser("test", help="Run tests")
    test_parser.add_argument("--test-cmd", help="Test command to run")
    test_parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")

    # Commit command
    commit_parser = subparsers.add_parser("commit", help="Generate commit")
    commit_parser.add_argument("--message", help="Custom commit prompt")
    commit_parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    commit_parser.add_argument("--dry-run", action="store_true", help="Dry run mode")

    # Version command
    subparsers.add_parser("version", help="Show version")

    # Models command
    models_parser = subparsers.add_parser("models", help="List AI models")
    models_parser.add_argument("pattern", nargs="?", help="Filter pattern")
    models_parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")

    # Config command
    config_parser = subparsers.add_parser("config", help="Manage configuration")
    config_parser.add_argument("--show", action="store_true", help="Show config")
    config_parser.add_argument("--edit", action="store_true", help="Edit config")
    config_parser.add_argument("--init", action="store_true", help="Initialize config")

    return parser

def handle_chat(args):
    """Handle chat command."""
    print("🤖 Starting AI coding session...")

    # Convert to legacy format
    argv = []
    if args.files:
        argv.extend(args.files)
    if args.model:
        argv.extend(["--model", args.model])
    if args.message:
        argv.extend(["--message", args.message])
    if args.verbose:
        argv.append("--verbose")
    if args.dry_run:
        argv.append("--dry-run")

    # Import and call legacy main
    try:
        from .main import main as legacy_main
        return legacy_main(argv)
    except ImportError:
        from main import main as legacy_main
        return legacy_main(argv)

def handle_wellness_check(args):
    """Handle wellness check command."""
    print(f"🏥 Checking code wellness for: {args.path}")

    path = Path(args.path)
    if not path.exists():
        print(f"❌ Path not found: {path}")
        return 1

    # Basic wellness checks
    issues = []

    # Check if it's a git repo (check current dir and parents)
    import subprocess
    try:
        result = subprocess.run(["git", "rev-parse", "--git-dir"],
                              cwd=path, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Git repository detected")
        else:
            issues.append("Not a git repository")
    except Exception:
        issues.append("Git not available or not a git repository")

    # Check for common files
    common_files = ["README.md", "requirements.txt", "pyproject.toml", ".gitignore"]
    for file in common_files:
        if (path / file).exists():
            print(f"✅ Found {file}")
        else:
            print(f"⚠️  Missing {file}")

    # Check for Python files
    py_files = list(path.glob("**/*.py"))
    if py_files:
        print(f"✅ Found {len(py_files)} Python files")
    else:
        print("⚠️  No Python files found")

    if issues:
        print("\n❌ Issues found:")
        for issue in issues:
            print(f"  • {issue}")
        return 1
    else:
        print("\n🎉 Basic wellness check passed!")
        return 0

def handle_version(args=None):
    """Handle version command."""
    try:
        from . import __version__
        version = __version__
    except ImportError:
        try:
            import __init__ as version_module
            version = getattr(version_module, '__version__', '0.1.0')
        except ImportError:
            version = "0.1.0"

    print(f"🏥 WCA Code version {version}")

def handle_models(args=None):
    """Handle models command."""
    print("🤖 Available AI models:")
    print("  • gpt-4")
    print("  • gpt-3.5-turbo")
    print("  • claude-3-sonnet")
    print("  • claude-3-haiku")
    print("\nUse --pattern to filter models")

def handle_config(args):
    """Handle config command."""
    if args.show:
        print("📋 Configuration files:")
        config_files = [
            Path.home() / ".aider.conf.yml",
            Path.cwd() / ".aider.conf.yml",
        ]
        for config_file in config_files:
            status = "exists" if config_file.exists() else "not found"
            print(f"  {config_file} - {status}")

    elif args.init:
        config_file = Path.cwd() / ".aider.conf.yml"
        if config_file.exists():
            response = input(f"Config file {config_file} exists. Overwrite? (y/N): ")
            if response.lower() != 'y':
                return

        sample_config = """# WCA Code Configuration
model: gpt-4
auto-commits: true
stream: true
pretty: true
"""
        config_file.write_text(sample_config)
        print(f"✅ Created config file: {config_file}")

    elif args.edit:
        import os
        import subprocess
        config_file = Path.cwd() / ".aider.conf.yml"
        if not config_file.exists():
            config_file.write_text("# WCA Code Configuration\n")

        editor = os.environ.get("EDITOR", "nano")
        subprocess.run([editor, str(config_file)])

    else:
        print("Use --show, --edit, or --init")

def handle_lint(args):
    """Handle lint command."""
    print("🔍 Linting code...")
    argv = ["--lint"]
    if args.files:
        argv.extend(args.files)
    if args.verbose:
        argv.append("--verbose")

    try:
        from .main import main as legacy_main
        return legacy_main(argv)
    except ImportError:
        from main import main as legacy_main
        return legacy_main(argv)

def handle_test(args):
    """Handle test command."""
    print("🧪 Running tests...")
    argv = ["--test"]
    if args.test_cmd:
        argv.extend(["--test-cmd", args.test_cmd])
    if args.verbose:
        argv.append("--verbose")

    try:
        from .main import main as legacy_main
        return legacy_main(argv)
    except ImportError:
        from main import main as legacy_main
        return legacy_main(argv)

def handle_commit(args):
    """Handle commit command."""
    print("📝 Generating commit...")
    argv = ["--commit"]
    if args.message:
        argv.extend(["--commit-prompt", args.message])
    if args.verbose:
        argv.append("--verbose")
    if args.dry_run:
        argv.append("--dry-run")

    try:
        from .main import main as legacy_main
        return legacy_main(argv)
    except ImportError:
        from main import main as legacy_main
        return legacy_main(argv)

def main():
    """Main entry point for simple CLI."""
    parser = create_simple_parser()

    if len(sys.argv) == 1:
        print("🏥 WCA Code - Your AI Code Wellness Coach")
        print("\nQuick start:")
        print("  wca-code chat           - Start AI coding session")
        print("  wca-code wellness-check - Check code health")
        print("  wca-code --help         - Show all commands")
        return 0

    args = parser.parse_args()

    if args.command == "chat":
        return handle_chat(args)
    elif args.command == "wellness-check":
        return handle_wellness_check(args)
    elif args.command == "version":
        return handle_version(args)
    elif args.command == "models":
        return handle_models(args)
    elif args.command == "config":
        return handle_config(args)
    elif args.command == "lint":
        return handle_lint(args)
    elif args.command == "test":
        return handle_test(args)
    elif args.command == "commit":
        return handle_commit(args)
    else:
        parser.print_help()
        return 1

if __name__ == "__main__":
    sys.exit(main())
