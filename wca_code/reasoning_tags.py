#!/usr/bin/env python

"""
Handles reasoning content formatting and manipulation in AI responses.
"""

import re
from dataclasses import dataclass
from typing import Optional


@dataclass
class ReasoningFormatting:
    """Standard formatting for reasoning sections."""
    start_marker: str = "--------------\n► **THINKING**"
    end_marker: str = "------------\n► **ANSWER**"


class ReasoningTag:
    """Manages reasoning content tags and formatting."""
    
    DEFAULT_TAG = "thinking-content-7bbeb8e1441453ad999a0bbba8a46d4b"

    def __init__(self, tag_name: str = DEFAULT_TAG, formatting: Optional[ReasoningFormatting] = None):
        """
        Initialize ReasoningTag with custom tag name and formatting.
        
        Args:
            tag_name: Custom tag identifier
            formatting: Custom formatting configuration
        """
        self.tag_name = tag_name
        self.formatting = formatting or ReasoningFormatting()

    def remove_content(self, text: str) -> str:
        """
        Remove reasoning content from text based on tags.
        
        Args:
            text: The text to process
            
        Returns:
            Text with reasoning content removed
        """
        if not self.tag_name:
            return text

        # Try to match the complete tag pattern first
        pattern = f"<{self.tag_name}>.*?</{self.tag_name}>"
        result = re.sub(pattern, "", text, flags=re.DOTALL).strip()

        # Handle case where closing tag exists but opening tag might be missing
        closing_tag = f"</{self.tag_name}>"
        if closing_tag in result:
            parts = result.split(closing_tag, 1)
            result = parts[1].strip() if len(parts) > 1 else result

        return result

    def replace_tags(self, text: str) -> str:
        """
        Replace reasoning tags with standard formatting.
        
        Args:
            text: The text containing the tags
            
        Returns:
            Text with reasoning tags replaced with standard format
        """
        if not text:
            return text

        # Replace opening tag with proper spacing
        text = re.sub(
            f"\\s*<{self.tag_name}>\\s*",
            f"\n{self.formatting.start_marker}\n\n",
            text
        )

        # Replace closing tag with proper spacing
        text = re.sub(
            f"\\s*</{self.tag_name}>\\s*",
            f"\n\n{self.formatting.end_marker}\n\n",
            text
        )

        return text

    def format_content(self, content: str) -> str:
        """
        Format reasoning content with appropriate tags.
        
        Args:
            content: The content to format
            
        Returns:
            Formatted reasoning content with tags
        """
        if not content:
            return ""

        return f"<{self.tag_name}>\n\n{content}\n\n</{self.tag_name}>"
