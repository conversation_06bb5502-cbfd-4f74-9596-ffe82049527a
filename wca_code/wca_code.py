"""
CLI entry point for wca_code.
A code wellness coach that helps you keep your codebase healthy.
"""

import json
import os
import re
import sys
import threading
import traceback
import webbrowser
from dataclasses import fields
from pathlib import Path

try:
    import git
except ImportError:
    git = None

import importlib_resources
import shtab
from dotenv import load_dotenv
from prompt_toolkit.enums import EditingMode

# Import all required modules from wca_code
from . import __version__, models, urls, utils
from .analytics import Analytics
from .args import get_parser
from .coders import Coder
from .coders.base_coder import UnknownEditFormat
from .commands import Commands, SwitchCoder
from .copypaste import ClipboardWatcher
from .deprecated import handle_deprecated_model_args
from .format_settings import format_settings, scrub_sensitive_info
from .history import ChatSummary
from .io import InputOutput
from .llm import litellm  # noqa: F401; properly init litellm on launch
from .models import ModelSettings
from .onboarding import offer_openrouter_oauth, select_default_model
from .repo import ANY_GIT_ERROR, GitRepo
from .report import report_uncaught_exceptions
from .versioncheck import check_version, install_from_main_branch, install_upgrade
from .watch import FileWatcher


def get_git_root():
    """Try and guess the git repo, since the conf.yml can be at the repo root"""
    try:
        repo = git.Repo(search_parent_directories=True)
        return repo.working_tree_dir
    except (git.InvalidGitRepositoryError, FileNotFoundError):
        return None


def main(argv=None, input=None, output=None, force_git_root=None, return_coder=False):
    """
    Main entry point for wca_code CLI.
    """
    report_uncaught_exceptions()

    if argv is None:
        argv = sys.argv[1:]

    if git is None:
        git_root = None
    elif force_git_root:
        git_root = force_git_root
    else:
        git_root = get_git_root()

    # All the main logic - formatting kept same as original aider.main
    parser = get_parser(default_config_files=[], git_root=git_root)
    try:
        args, unknown = parser.parse_known_args(argv)
    except AttributeError as e:
        raise e

    io = InputOutput(pretty=True)
    analytics = Analytics()
    
    # Set up the coder
    coder = Coder.create(
        main_model=None,  # You'll need to configure this
        io=io,
        verbose=True
    )

    if return_coder:
        return coder

    analytics.event("cli session") 

    # Main CLI loop
    while True:
        try:
            coder.run()
            analytics.event("exit", reason="Completed main CLI coder.run")
            return
        except SwitchCoder as switch:
            coder = Coder.create(io=io, from_coder=coder, **switch.kwargs)
            coder.show_announcements()


if __name__ == "__main__":
    status = main()
    sys.exit(status)
