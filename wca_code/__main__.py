"""
Entry point for WCA Code CLI.
Supports both legacy argparse mode and new Typer mode.
"""

import sys

def main():
    """Main entry point that chooses between legacy and modern CLI."""

    # Check if user wants legacy mode
    if "--legacy" in sys.argv:
        sys.argv.remove("--legacy")
        from .main import main as legacy_main
        return legacy_main()

    # Check if this is being called as a module (python -m wca_code)
    # In that case, default to legacy for compatibility
    if __name__ == "__main__" and len(sys.argv) > 1:
        # If it looks like legacy arguments, use legacy mode
        legacy_indicators = [
            "--model", "--message", "--files", "--git", "--no-git",
            "--stream", "--no-stream", "--verbose", "-v"
        ]
        if any(arg in sys.argv for arg in legacy_indicators):
            from .main import main as legacy_main
            return legacy_main()

    # Use modern Typer CLI by default
    try:
        from .cli_main import app
        app()
    except ImportError:
        # Try simple CLI if Typer not available
        try:
            from .cli_simple import main as simple_main
            return simple_main()
        except ImportError:
            # Final fallback to legacy
            from .main import main as legacy_main
            return legacy_main()
    except Exception as e:
        # If Typer CLI fails, try simple CLI
        try:
            print(f"Modern CLI failed: {e}")
            print("Trying simple CLI...")
            from .cli_simple import main as simple_main
            return simple_main()
        except Exception:
            print("Falling back to legacy mode...")
            from .main import main as legacy_main
            return legacy_main()


if __name__ == "__main__":
    main()
