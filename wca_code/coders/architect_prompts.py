# flake8: noqa: E501

from ..prompts import architect
from .base_prompts import CoderPrompts


class ArchitectPrompts(CoderPrompts):
    main_system = architect.MAIN_SYSTEM
    example_messages = architect.EXAMPLE_MESSAGES
    files_content_prefix = architect.FILES_CONTENT_PREFIX
    files_content_assistant_reply = architect.FILES_CONTENT_ASSISTANT_REPLY

    files_no_full_files = architect.FILES_NO_FULL_FILES
    files_no_full_files_with_repo_map = architect.FILES_NO_FULL_FILES_WITH_REPO_MAP
    files_no_full_files_with_repo_map_reply = architect.FILES_NO_FULL_FILES_WITH_REPO_MAP_REPLY
    repo_content_prefix = architect.REPO_CONTENT_PREFIX
    system_reminder = architect.SYSTEM_REMINDER
