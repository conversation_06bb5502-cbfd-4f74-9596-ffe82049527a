"""
Main CLI entry point for WCA Code.
Integrates all CLI modules and provides the primary interface.
"""

import sys
from pathlib import Path
from typing import Optional
import typer
from rich.console import Console
from rich.panel import Panel
from rich import print as rprint

# Import CLI modules
from .cli import app as core_app
from .cli_wellness import wellness_app

# Create the main application
app = typer.Typer(
    name="wca-code",
    help="🏥 WCA Code - Your AI Code Wellness Coach",
    add_completion=False,
    rich_markup_mode="rich",
    no_args_is_help=True,
    context_settings={"help_option_names": ["-h", "--help"]},
)

console = Console()

# Add the core app commands directly to main app
app.add_typer(core_app, name="core", hidden=True)  # Hidden, but commands available at root
app.add_typer(wellness_app, name="wellness")

# Copy core commands to root level for convenience
@app.command()
def chat(
    files: Optional[list] = typer.Argument(None, help="Files to edit with AI"),
    model: Optional[str] = typer.Option(None, help="AI model to use"),
    message: Optional[str] = typer.Option(None, "-m", "--message", help="Single message to send"),
    verbose: bool = typer.Option(False, "-v", "--verbose", help="Enable verbose output"),
):
    """Start an interactive AI coding session."""
    # Delegate to core app
    from .cli import chat as core_chat
    return core_chat(files, model, message, verbose)


@app.command()
def version():
    """Show version information."""
    from .cli import version as core_version
    return core_version()


@app.command()
def models(
    pattern: Optional[str] = typer.Argument(None, help="Pattern to filter models"),
    verbose: bool = typer.Option(False, "-v", "--verbose", help="Enable verbose output"),
):
    """List available AI models."""
    from .cli import models as core_models
    return core_models(pattern, verbose)


@app.command()
def lint(
    files: Optional[list] = typer.Argument(None, help="Files to lint"),
    fix: bool = typer.Option(False, help="Automatically fix issues"),
    verbose: bool = typer.Option(False, "-v", "--verbose", help="Enable verbose output"),
):
    """Lint and optionally fix code issues."""
    from .cli import lint as core_lint
    return core_lint(files, fix, verbose)


@app.command()
def test(
    test_cmd: Optional[str] = typer.Option(None, help="Command to run tests"),
    verbose: bool = typer.Option(False, "-v", "--verbose", help="Enable verbose output"),
):
    """Run tests and fix any issues found."""
    from .cli import test as core_test
    return core_test(test_cmd, verbose)


@app.command()
def commit(
    message: Optional[str] = typer.Option(None, help="Custom commit message prompt"),
    verbose: bool = typer.Option(False, "-v", "--verbose", help="Enable verbose output"),
    dry_run: bool = typer.Option(False, help="Perform a dry run"),
):
    """Generate and create a commit for pending changes."""
    from .cli import commit as core_commit
    return core_commit(message, verbose, dry_run)


@app.command()
def watch(
    files: Optional[list] = typer.Argument(None, help="Files to watch"),
    model: Optional[str] = typer.Option(None, help="AI model to use"),
    verbose: bool = typer.Option(False, "-v", "--verbose", help="Enable verbose output"),
):
    """Watch files for AI coding comments and auto-respond."""
    from .cli import watch as core_watch
    return core_watch(files, model, verbose)


@app.command()
def config(
    show: bool = typer.Option(False, "--show", help="Show current configuration"),
    edit: bool = typer.Option(False, "--edit", help="Edit configuration file"),
    init: bool = typer.Option(False, "--init", help="Initialize configuration"),
):
    """Manage WCA Code configuration."""
    from .cli import config as core_config
    return core_config(show, edit, init)


@app.command("repo-map")
def repo_map(
    verbose: bool = typer.Option(False, "-v", "--verbose", help="Enable verbose output"),
):
    """Show the repository map."""
    from .cli import repo_map as core_repo_map
    return core_repo_map(verbose)


@app.callback(invoke_without_command=True)
def main(
    ctx: typer.Context,
    version: bool = typer.Option(False, "--version", help="Show version and exit"),
):
    """
    🏥 WCA Code - Your AI Code Wellness Coach
    
    A modern CLI tool that helps you maintain healthy, high-quality code
    using AI assistance and automated wellness checks.
    
    Common commands:
    • wca-code chat [files]     - Start AI coding session
    • wca-code wellness check   - Check code health
    • wca-code lint [files]     - Lint and fix code
    • wca-code commit           - Generate smart commits
    """
    
    if version:
        from . import __version__
        rprint(f"[bold blue]WCA Code[/bold blue] version [green]{__version__}[/green]")
        return
    
    if ctx.invoked_subcommand is None:
        # Show welcome message and help
        welcome_panel = Panel.fit(
            "[bold blue]🏥 WCA Code[/bold blue]\n"
            "[dim]Your AI Code Wellness Coach[/dim]\n\n"
            "Get started:\n"
            "• [cyan]wca-code chat[/cyan] - Start AI coding session\n"
            "• [cyan]wca-code wellness check[/cyan] - Check code health\n"
            "• [cyan]wca-code --help[/cyan] - Show all commands",
            title="Welcome",
            border_style="blue"
        )
        console.print(welcome_panel)
        
        # Quick status check
        try:
            from .cli_wellness import check_git_health
            git_health = check_git_health(Path.cwd())
            if git_health["status"] == "healthy":
                console.print("\n[green]✅ Git repository detected[/green]")
            elif git_health["status"] == "no_git":
                console.print("\n[yellow]⚠️  Not in a git repository[/yellow]")
        except Exception:
            pass


# Add git subcommands
git_app = typer.Typer(name="git", help="Git-related commands")
app.add_typer(git_app, name="git")

@git_app.command("status")
def git_status():
    """Show git status with AI insights."""
    from .cli import git_status as core_git_status
    return core_git_status()


@git_app.command("diff")
def git_diff(
    files: Optional[list] = typer.Argument(None, help="Files to diff"),
):
    """Show git diff with AI analysis."""
    from .cli import git_diff as core_git_diff
    return core_git_diff(files)


# Error handling
@app.command("doctor", hidden=True)
def doctor():
    """Diagnose common issues with WCA Code setup."""
    console.print("[bold blue]🩺 WCA Code Doctor[/bold blue]\n")
    
    issues = []
    
    # Check Python version
    if sys.version_info < (3, 8):
        issues.append("Python 3.8+ required")
    else:
        console.print("[green]✅ Python version OK[/green]")
    
    # Check git availability
    import shutil
    if not shutil.which("git"):
        issues.append("Git not found in PATH")
    else:
        console.print("[green]✅ Git available[/green]")
    
    # Check if in git repo
    try:
        import subprocess
        result = subprocess.run(["git", "rev-parse", "--git-dir"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            console.print("[green]✅ In git repository[/green]")
        else:
            console.print("[yellow]⚠️  Not in git repository[/yellow]")
    except Exception:
        issues.append("Cannot check git repository status")
    
    # Check dependencies
    try:
        import typer, rich
        console.print("[green]✅ Core dependencies available[/green]")
    except ImportError as e:
        issues.append(f"Missing dependency: {e}")
    
    if issues:
        console.print("\n[red]Issues found:[/red]")
        for issue in issues:
            console.print(f"  • {issue}")
    else:
        console.print("\n[green]🎉 All checks passed![/green]")


if __name__ == "__main__":
    app()
