"""
Wellness-focused CLI commands for WCA Code.
Commands specifically designed for code health and quality.
"""

import os
import subprocess
from pathlib import Path
from typing import List, Optional, Annotated
import typer
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import print as rprint

console = Console()

# Create wellness-focused app
wellness_app = typer.Typer(name="wellness", help="🏥 Code wellness and health commands")


@wellness_app.command("check")
def wellness_check(
    path: Annotated[Optional[Path], typer.Argument(help="Path to check")] = None,
    detailed: Annotated[bool, typer.Option("--detailed", help="Show detailed analysis")] = False,
):
    """Perform a comprehensive code wellness check."""
    
    if path is None:
        path = Path.cwd()
    
    console.print(f"[bold blue]🏥 Code Wellness Check for {path}[/bold blue]\n")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        
        # Git health check
        task = progress.add_task("Checking git repository health...", total=None)
        git_health = check_git_health(path)
        progress.update(task, description="✅ Git health checked")
        
        # Code quality check
        task = progress.add_task("Analyzing code quality...", total=None)
        code_quality = check_code_quality(path)
        progress.update(task, description="✅ Code quality analyzed")
        
        # Dependencies check
        task = progress.add_task("Checking dependencies...", total=None)
        deps_health = check_dependencies(path)
        progress.update(task, description="✅ Dependencies checked")
        
        # Test coverage check
        task = progress.add_task("Analyzing test coverage...", total=None)
        test_health = check_test_coverage(path)
        progress.update(task, description="✅ Test coverage analyzed")
    
    # Display results
    display_wellness_report(git_health, code_quality, deps_health, test_health, detailed)


@wellness_app.command("score")
def wellness_score(
    path: Annotated[Optional[Path], typer.Argument(help="Path to score")] = None,
):
    """Calculate and display a wellness score for the codebase."""
    
    if path is None:
        path = Path.cwd()
    
    console.print(f"[bold blue]📊 Calculating Wellness Score for {path}[/bold blue]\n")
    
    # Calculate various metrics
    git_score = calculate_git_score(path)
    quality_score = calculate_quality_score(path)
    deps_score = calculate_deps_score(path)
    test_score = calculate_test_score(path)
    
    # Overall score (weighted average)
    overall_score = (git_score * 0.2 + quality_score * 0.3 + deps_score * 0.2 + test_score * 0.3)
    
    # Create score table
    table = Table(title="Wellness Score Breakdown")
    table.add_column("Category", style="cyan")
    table.add_column("Score", style="magenta")
    table.add_column("Status", style="green")
    
    table.add_row("Git Health", f"{git_score:.1f}/10", get_status_emoji(git_score))
    table.add_row("Code Quality", f"{quality_score:.1f}/10", get_status_emoji(quality_score))
    table.add_row("Dependencies", f"{deps_score:.1f}/10", get_status_emoji(deps_score))
    table.add_row("Test Coverage", f"{test_score:.1f}/10", get_status_emoji(test_score))
    table.add_row("", "", "")  # Separator
    table.add_row("[bold]Overall Score[/bold]", f"[bold]{overall_score:.1f}/10[/bold]", get_status_emoji(overall_score))
    
    console.print(table)
    
    # Recommendations
    if overall_score < 7:
        console.print("\n[yellow]💡 Recommendations for improvement:[/yellow]")
        if git_score < 7:
            console.print("  • Improve git practices (regular commits, meaningful messages)")
        if quality_score < 7:
            console.print("  • Address code quality issues (linting, complexity)")
        if deps_score < 7:
            console.print("  • Update dependencies and review security")
        if test_score < 7:
            console.print("  • Increase test coverage and add missing tests")


@wellness_app.command("trends")
def wellness_trends(
    days: Annotated[int, typer.Option("--days", help="Number of days to analyze")] = 30,
):
    """Show wellness trends over time."""
    
    console.print(f"[bold blue]📈 Wellness Trends (Last {days} days)[/bold blue]\n")
    
    # This would analyze git history and show trends
    console.print("[yellow]Feature coming soon: Historical wellness analysis[/yellow]")
    console.print("This will show:")
    console.print("  • Commit frequency trends")
    console.print("  • Code quality evolution")
    console.print("  • Test coverage changes")
    console.print("  • Dependency update patterns")


@wellness_app.command("fix")
def wellness_fix(
    auto: Annotated[bool, typer.Option("--auto", help="Automatically fix issues")] = False,
    category: Annotated[Optional[str], typer.Option(help="Fix specific category")] = None,
):
    """Automatically fix common wellness issues."""
    
    console.print("[bold blue]🔧 Fixing Wellness Issues[/bold blue]\n")
    
    if not auto:
        console.print("[yellow]This will attempt to fix common issues. Continue?[/yellow]")
        if not typer.confirm("Proceed with fixes?"):
            return
    
    fixes_applied = []
    
    # Fix git issues
    if category is None or category == "git":
        if fix_git_issues():
            fixes_applied.append("Git configuration")
    
    # Fix code quality issues
    if category is None or category == "quality":
        if fix_quality_issues():
            fixes_applied.append("Code formatting")
    
    # Fix dependency issues
    if category is None or category == "deps":
        if fix_dependency_issues():
            fixes_applied.append("Dependencies")
    
    if fixes_applied:
        console.print(f"[green]✅ Applied fixes: {', '.join(fixes_applied)}[/green]")
    else:
        console.print("[yellow]No issues found to fix[/yellow]")


# Helper functions
def check_git_health(path: Path) -> dict:
    """Check git repository health."""
    try:
        # Check if it's a git repo
        result = subprocess.run(["git", "rev-parse", "--git-dir"], 
                              cwd=path, capture_output=True, text=True)
        if result.returncode != 0:
            return {"status": "no_git", "issues": ["Not a git repository"]}
        
        # Check for uncommitted changes
        result = subprocess.run(["git", "status", "--porcelain"], 
                              cwd=path, capture_output=True, text=True)
        uncommitted = len(result.stdout.strip().split('\n')) if result.stdout.strip() else 0
        
        # Check recent commit activity
        result = subprocess.run(["git", "log", "--oneline", "-10"], 
                              cwd=path, capture_output=True, text=True)
        recent_commits = len(result.stdout.strip().split('\n')) if result.stdout.strip() else 0
        
        return {
            "status": "healthy",
            "uncommitted_files": uncommitted,
            "recent_commits": recent_commits,
            "issues": []
        }
    except Exception as e:
        return {"status": "error", "issues": [str(e)]}


def check_code_quality(path: Path) -> dict:
    """Check code quality metrics."""
    # This would integrate with linters, complexity analyzers, etc.
    return {
        "status": "good",
        "linting_errors": 0,
        "complexity_issues": 0,
        "issues": []
    }


def check_dependencies(path: Path) -> dict:
    """Check dependency health."""
    # This would check for outdated packages, security vulnerabilities, etc.
    return {
        "status": "good",
        "outdated_packages": 0,
        "security_issues": 0,
        "issues": []
    }


def check_test_coverage(path: Path) -> dict:
    """Check test coverage."""
    # This would run coverage tools
    return {
        "status": "good",
        "coverage_percentage": 85,
        "missing_tests": [],
        "issues": []
    }


def calculate_git_score(path: Path) -> float:
    """Calculate git health score."""
    health = check_git_health(path)
    if health["status"] == "no_git":
        return 0.0
    if health["status"] == "error":
        return 2.0
    
    score = 10.0
    score -= min(health["uncommitted_files"] * 0.5, 3.0)  # Penalty for uncommitted files
    score += min(health["recent_commits"] * 0.2, 2.0)     # Bonus for activity
    
    return max(0.0, min(10.0, score))


def calculate_quality_score(path: Path) -> float:
    """Calculate code quality score."""
    # Placeholder - would integrate with actual quality tools
    return 8.5


def calculate_deps_score(path: Path) -> float:
    """Calculate dependencies score."""
    # Placeholder - would check actual dependencies
    return 7.8


def calculate_test_score(path: Path) -> float:
    """Calculate test coverage score."""
    # Placeholder - would check actual test coverage
    return 8.2


def get_status_emoji(score: float) -> str:
    """Get status emoji based on score."""
    if score >= 9:
        return "🟢 Excellent"
    elif score >= 7:
        return "🟡 Good"
    elif score >= 5:
        return "🟠 Fair"
    else:
        return "🔴 Poor"


def display_wellness_report(git_health, code_quality, deps_health, test_health, detailed=False):
    """Display comprehensive wellness report."""
    console.print("[bold green]📋 Wellness Report[/bold green]\n")
    
    # Summary table
    table = Table(title="Health Summary")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Issues", style="red")
    
    table.add_row("Git Repository", git_health["status"], str(len(git_health["issues"])))
    table.add_row("Code Quality", code_quality["status"], str(len(code_quality["issues"])))
    table.add_row("Dependencies", deps_health["status"], str(len(deps_health["issues"])))
    table.add_row("Test Coverage", test_health["status"], str(len(test_health["issues"])))
    
    console.print(table)
    
    if detailed:
        # Show detailed issues
        all_issues = (git_health["issues"] + code_quality["issues"] + 
                     deps_health["issues"] + test_health["issues"])
        if all_issues:
            console.print("\n[bold red]Issues Found:[/bold red]")
            for issue in all_issues:
                console.print(f"  • {issue}")


def fix_git_issues() -> bool:
    """Fix common git issues."""
    # Placeholder for git fixes
    return False


def fix_quality_issues() -> bool:
    """Fix code quality issues."""
    # Placeholder for quality fixes
    return False


def fix_dependency_issues() -> bool:
    """Fix dependency issues."""
    # Placeholder for dependency fixes
    return False
