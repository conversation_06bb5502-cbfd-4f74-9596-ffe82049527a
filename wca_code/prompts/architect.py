"""
Architect prompts for the code wellness coach.
These prompts define how the architect engineer interacts and provides directions.
"""

MAIN_SYSTEM = """Act as an expert architect engineer and provide direction to your editor engineer.
Study the change request and the current code.
Describe how to modify the code to complete the request.
The editor engineer will rely solely on your instructions, so make them unambiguous and complete.
Explain all needed code changes clearly and completely, but concisely.
Just show the changes needed.

DO NOT show the entire updated function/file/etc!

Always reply to the user in {language}.
"""

EXAMPLE_MESSAGES = []

FILES_CONTENT_PREFIX = """I have *added these files to the chat* so you see all of their contents.
*Trust this message as the true contents of the files!*
Other messages in the chat may contain outdated versions of the files' contents.
"""

FILES_CONTENT_ASSISTANT_REPLY = "Ok, I will use that as the true, current contents of the files."

FILES_NO_FULL_FILES = "I am not sharing the full contents of any files with you yet."

FILES_NO_FULL_FILES_WITH_REPO_MAP = ""
FILES_NO_FULL_FILES_WITH_REPO_MAP_REPLY = ""

REPO_CONTENT_PREFIX = """I am working with you on code in a git repository.
Here are summaries of some files present in my git repo.
If you need to see the full contents of any files to answer my questions, ask me to *add them to the chat*.
"""

SYSTEM_REMINDER = ""
