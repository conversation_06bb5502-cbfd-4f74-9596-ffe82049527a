"""
Enhanced WCA Code CLI with complete aider feature parity.
Includes all advanced features: voice, GUI, scraping, file watching, etc.
"""

import os
import sys
from pathlib import Path
from typing import Optional, List, Dict, Any, Annotated

try:
    import typer
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.text import Text
    from rich import print as rprint
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.prompt import Prompt, Confirm
    from rich.syntax import Syntax
    from rich.tree import Tree
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

# Import WCA Code modules
try:
    from . import __version__
    from .main import main as legacy_main
    from .models import Model
    from .io import InputOutput
    from .coders import Coder
    from .voice import Voice
    from .gui import gui_main
    from .scrape import Scraper, has_playwright
    from .watch import FileWatcher
    from .linter import Linter
    from .analytics import Analytics
except ImportError as e:
    print(f"Warning: Some advanced features unavailable: {e}")

# Initialize enhanced app
app = typer.Typer(
    name="wca-code",
    help="🚀 WCA Code - Complete AI Coding Assistant with Advanced Features",
    add_completion=False,
    rich_markup_mode="rich" if RICH_AVAILABLE else None,
    context_settings={"help_option_names": ["-h", "--help"]}
)

console = Console() if RICH_AVAILABLE else None

# Common type annotations
ModelOption = Annotated[Optional[str], typer.Option("--model", "-m", help="AI model to use")]
VerboseOption = Annotated[bool, typer.Option("--verbose", "-v", help="Enable verbose output")]
StreamOption = Annotated[bool, typer.Option("--stream/--no-stream", help="Enable/disable streaming")]
YesOption = Annotated[bool, typer.Option("--yes", "-y", help="Automatically answer yes to prompts")]

@app.command()
def version():
    """Show WCA Code version information."""
    if RICH_AVAILABLE:
        panel = Panel(
            f"[bold blue]WCA Code[/bold blue] v{__version__}\n"
            f"[dim]AI-powered coding assistant with wellness features[/dim]",
            title="Version Info",
            border_style="blue"
        )
        console.print(panel)
    else:
        print(f"WCA Code v{__version__}")

@app.command()
def chat(
    files: Optional[List[str]] = typer.Argument(None, help="Files to include in chat"),
    model: ModelOption = "gpt-4",
    message: Optional[str] = typer.Option(None, "--message", "-m", help="Single message to send"),
    message_file: Optional[str] = typer.Option(None, "--message-file", "-f", help="File containing message"),
    stream: StreamOption = True,
    verbose: VerboseOption = False,
    yes: YesOption = False,
    auto_commits: bool = typer.Option(True, help="Enable automatic commits"),
    auto_lint: bool = typer.Option(True, help="Enable automatic linting"),
    auto_test: bool = typer.Option(False, help="Enable automatic testing"),
    map_tokens: int = typer.Option(1024, help="Tokens for repository map"),
    edit_format: Optional[str] = typer.Option(None, help="Edit format (diff, whole, etc.)"),
    architect: bool = typer.Option(False, help="Use architect mode"),
    dry_run: bool = typer.Option(False, help="Perform dry run without changes"),
):
    """Start an interactive AI coding session."""
    if RICH_AVAILABLE:
        console.print("[bold green]🤖 Starting WCA Code chat session...[/bold green]")

    # Build arguments for legacy main
    args = []
    if files:
        args.extend(files)

    # Add options
    if model != "gpt-4":
        args.extend(["--model", model])
    if message:
        args.extend(["--message", message])
    if message_file:
        args.extend(["--message-file", message_file])
    if not stream:
        args.append("--no-stream")
    if verbose:
        args.append("--verbose")
    if yes:
        args.append("--yes")
    if not auto_commits:
        args.append("--no-auto-commits")
    if not auto_lint:
        args.append("--no-auto-lint")
    if auto_test:
        args.append("--auto-test")
    if map_tokens != 1024:
        args.extend(["--map-tokens", str(map_tokens)])
    if edit_format:
        args.extend(["--edit-format", edit_format])
    if architect:
        args.append("--architect")
    if dry_run:
        args.append("--dry-run")

    # Call legacy main with arguments
    return legacy_main(args)

@app.command()
def voice(
    files: Optional[List[str]] = typer.Argument(None, help="Files to include"),
    model: ModelOption = "gpt-4",
    language: Optional[str] = typer.Option("en", help="Voice language (ISO 639-1 code)"),
    audio_format: str = typer.Option("wav", help="Audio format (wav, mp3, webm)"),
    device: Optional[str] = typer.Option(None, help="Audio input device name"),
):
    """Start voice-enabled coding session."""
    try:
        if RICH_AVAILABLE:
            console.print("[bold blue]🎤 Starting voice session...[/bold blue]")

        voice = Voice(audio_format=audio_format, device_name=device)

        # Start voice session
        args = ["--voice-language", language, "--voice-format", audio_format]
        if files:
            args.extend(files)
        if model != "gpt-4":
            args.extend(["--model", model])
        if device:
            args.extend(["--voice-input-device", device])

        return legacy_main(args)

    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Voice session failed: {e}[/red]")
        else:
            print(f"Voice session failed: {e}")
        return 1

@app.command()
def gui(
    port: int = typer.Option(8501, help="Port for web interface"),
    browser: bool = typer.Option(True, help="Open browser automatically"),
):
    """Launch WCA Code in browser interface."""
    try:
        if RICH_AVAILABLE:
            console.print(f"[bold green]🌐 Starting web interface on port {port}...[/bold green]")

        # Set environment for Streamlit
        os.environ["STREAMLIT_SERVER_PORT"] = str(port)
        if not browser:
            os.environ["STREAMLIT_SERVER_HEADLESS"] = "true"

        # Launch GUI
        gui_main()

    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ GUI failed to start: {e}[/red]")
        else:
            print(f"GUI failed to start: {e}")
        return 1

@app.command()
def scrape(
    url: str = typer.Argument(..., help="URL to scrape"),
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file"),
    format: str = typer.Option("markdown", help="Output format (markdown, html, text)"),
):
    """Scrape web content and convert to markdown."""
    try:
        if RICH_AVAILABLE:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task(f"Scraping {url}...", total=None)

                scraper = Scraper(playwright_available=has_playwright())
                content = scraper.scrape(url)

                progress.update(task, description="✅ Scraping complete")
        else:
            print(f"Scraping {url}...")
            scraper = Scraper(playwright_available=has_playwright())
            content = scraper.scrape(url)

        if not content:
            if RICH_AVAILABLE:
                console.print("[red]❌ Failed to scrape content[/red]")
            else:
                print("Failed to scrape content")
            return 1

        if output:
            Path(output).write_text(content, encoding='utf-8')
            if RICH_AVAILABLE:
                console.print(f"[green]✅ Content saved to {output}[/green]")
            else:
                print(f"Content saved to {output}")
        else:
            print(content)

        return 0

    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Scraping failed: {e}[/red]")
        else:
            print(f"Scraping failed: {e}")
        return 1

@app.command()
def watch(
    path: str = typer.Argument(".", help="Directory to watch"),
    model: ModelOption = "gpt-4",
    verbose: VerboseOption = False,
):
    """Watch files for AI coding comments and auto-process them."""
    try:
        if RICH_AVAILABLE:
            console.print(f"[bold yellow]👁️ Watching {path} for AI comments...[/bold yellow]")
            console.print("[dim]Add comments like '# ai: fix this bug' to trigger processing[/dim]")

        # Start file watcher
        args = ["--watch-files", "--model", model]
        if verbose:
            args.append("--verbose")

        return legacy_main(args)

    except KeyboardInterrupt:
        if RICH_AVAILABLE:
            console.print("\n[yellow]👋 File watching stopped[/yellow]")
        else:
            print("\nFile watching stopped")
        return 0
    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ File watching failed: {e}[/red]")
        else:
            print(f"File watching failed: {e}")
        return 1

@app.command()
def lint(
    files: Optional[List[str]] = typer.Argument(None, help="Files to lint"),
    fix: bool = typer.Option(True, help="Automatically fix issues"),
    model: ModelOption = "gpt-4",
):
    """Lint and fix code issues using AI."""
    try:
        if RICH_AVAILABLE:
            console.print("[bold blue]🔍 Running AI-powered linting...[/bold blue]")

        args = ["--lint"]
        if files:
            args.extend(files)
        if model != "gpt-4":
            args.extend(["--model", model])

        return legacy_main(args)

    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Linting failed: {e}[/red]")
        else:
            print(f"Linting failed: {e}")
        return 1

@app.command()
def test(
    files: Optional[List[str]] = typer.Argument(None, help="Files to test"),
    model: ModelOption = "gpt-4",
    test_cmd: Optional[str] = typer.Option(None, help="Custom test command"),
):
    """Run tests and fix failures using AI."""
    try:
        if RICH_AVAILABLE:
            console.print("[bold green]🧪 Running AI-powered testing...[/bold green]")

        args = ["--test"]
        if files:
            args.extend(files)
        if model != "gpt-4":
            args.extend(["--model", model])
        if test_cmd:
            args.extend(["--test-cmd", test_cmd])

        return legacy_main(args)

    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Testing failed: {e}[/red]")
        else:
            print(f"Testing failed: {e}")
        return 1

@app.command()
def commit(
    message: Optional[str] = typer.Option(None, help="Custom commit message prompt"),
    model: ModelOption = "gpt-4",
):
    """Generate and create AI-powered commit messages."""
    try:
        if RICH_AVAILABLE:
            console.print("[bold cyan]📝 Generating AI commit message...[/bold cyan]")

        args = ["--commit"]
        if message:
            args.extend(["--commit-prompt", message])
        if model != "gpt-4":
            args.extend(["--model", model])

        return legacy_main(args)

    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Commit generation failed: {e}[/red]")
        else:
            print(f"Commit generation failed: {e}")
        return 1

@app.command()
def models(
    provider: Optional[str] = typer.Option(None, help="Filter by provider (openai, anthropic, etc.)"),
    show_pricing: bool = typer.Option(False, help="Show pricing information"),
):
    """List available AI models with details."""
    try:
        if RICH_AVAILABLE:
            console.print("[bold blue]🤖 Available AI Models[/bold blue]\n")

            table = Table(title="AI Models")
            table.add_column("Model", style="cyan", no_wrap=True)
            table.add_column("Provider", style="magenta")
            table.add_column("Context", style="green")
            table.add_column("Capabilities", style="yellow")
            if show_pricing:
                table.add_column("Pricing", style="red")

            # Sample models (would be loaded from models module)
            models_data = [
                ("gpt-4", "OpenAI", "128k", "Code, Chat, Vision"),
                ("gpt-4-turbo", "OpenAI", "128k", "Code, Chat, Vision"),
                ("claude-3-sonnet", "Anthropic", "200k", "Code, Chat, Analysis"),
                ("claude-3-opus", "Anthropic", "200k", "Code, Chat, Analysis"),
                ("gpt-3.5-turbo", "OpenAI", "16k", "Code, Chat"),
            ]

            for model_data in models_data:
                if provider and provider.lower() not in model_data[1].lower():
                    continue

                row = list(model_data)
                if show_pricing:
                    row.append("$0.01/1k tokens")  # Sample pricing

                table.add_row(*row)

            console.print(table)
        else:
            print("Available AI models:")
            print("- gpt-4 (OpenAI)")
            print("- gpt-4-turbo (OpenAI)")
            print("- claude-3-sonnet (Anthropic)")
            print("- claude-3-opus (Anthropic)")
            print("- gpt-3.5-turbo (OpenAI)")

        return 0

    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Failed to list models: {e}[/red]")
        else:
            print(f"Failed to list models: {e}")
        return 1

@app.command()
def config(
    show: bool = typer.Option(False, "--show", help="Show current configuration"),
    edit: bool = typer.Option(False, "--edit", help="Edit configuration file"),
    init: bool = typer.Option(False, "--init", help="Initialize configuration"),
    key: Optional[str] = typer.Option(None, help="Get/set specific config key"),
    value: Optional[str] = typer.Option(None, help="Value to set for config key"),
):
    """Manage WCA Code configuration."""
    try:
        if init:
            if RICH_AVAILABLE:
                console.print("[bold green]🔧 Initializing WCA Code configuration...[/bold green]")

            config_file = Path(".wca_code.conf.yml")
            if config_file.exists():
                if RICH_AVAILABLE:
                    overwrite = Confirm.ask("Configuration file exists. Overwrite?")
                else:
                    overwrite = input("Configuration file exists. Overwrite? (y/N): ").lower().startswith('y')

                if not overwrite:
                    return 0

            config_content = """# WCA Code Configuration
# See https://wca-code.example.com/docs/config.html for all options

# Model settings
model: gpt-4
# weak-model: gpt-3.5-turbo

# Git settings
auto-commits: true
dirty-commits: true

# Output settings
dark-mode: false
stream: true
pretty: true

# Advanced settings
map-tokens: 1024
cache-prompts: false
"""
            config_file.write_text(config_content)

            if RICH_AVAILABLE:
                console.print(f"[green]✅ Configuration initialized: {config_file}[/green]")
            else:
                print(f"Configuration initialized: {config_file}")

            return 0

        elif show:
            if RICH_AVAILABLE:
                console.print("[bold blue]📋 WCA Code Configuration[/bold blue]\n")

            config_files = [
                Path(".wca_code.conf.yml"),
                Path.home() / ".wca_code.conf.yml",
                Path.home() / ".config" / "wca_code" / "config.yml",
            ]

            found_configs = [f for f in config_files if f.exists()]

            if found_configs:
                for config_file in found_configs:
                    if RICH_AVAILABLE:
                        console.print(f"[cyan]📄 {config_file}[/cyan]")
                        content = config_file.read_text()
                        syntax = Syntax(content, "yaml", theme="monokai", line_numbers=True)
                        console.print(syntax)
                        console.print()
                    else:
                        print(f"Config file: {config_file}")
                        print(config_file.read_text())
                        print()
            else:
                if RICH_AVAILABLE:
                    console.print("[yellow]⚠️ No configuration files found[/yellow]")
                else:
                    print("No configuration files found")

            return 0

        elif edit:
            config_file = Path(".wca_code.conf.yml")
            if not config_file.exists():
                if RICH_AVAILABLE:
                    console.print("[yellow]⚠️ No config file found. Creating one...[/yellow]")
                # Initialize first
                return config(init=True)

            # Open in default editor
            import subprocess
            editor = os.environ.get('EDITOR', 'nano')
            subprocess.run([editor, str(config_file)])

            return 0

        else:
            if RICH_AVAILABLE:
                console.print("[yellow]Use --show, --edit, or --init[/yellow]")
            else:
                print("Use --show, --edit, or --init")
            return 1

    except Exception as e:
        if RICH_AVAILABLE:
            console.print(f"[red]❌ Configuration error: {e}[/red]")
        else:
            print(f"Configuration error: {e}")
        return 1

if __name__ == "__main__":
    app()
