#!/usr/bin/env python3
"""
Test runner for WCA Code functionality.
Provides easy commands to run different types of tests.
"""

import sys
import subprocess
import argparse
from pathlib import Path

def run_command(cmd, description):
    """Run a command and return success status."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False

def run_wca_cli_tests():
    """Run WCA CLI tests."""
    cmd = "python3 -m pytest tests/basic/test_wca_cli.py -v"
    return run_command(cmd, "Running WCA CLI tests")

def run_wca_wellness_tests():
    """Run WCA wellness tests."""
    cmd = "python3 -m pytest tests/basic/test_wca_wellness.py -v"
    return run_command(cmd, "Running WCA wellness tests")

def run_wca_config_tests():
    """Run WCA configuration tests."""
    cmd = "python3 -m pytest tests/basic/test_wca_config.py -v"
    return run_command(cmd, "Running WCA configuration tests")

def run_wca_main_entry_tests():
    """Run WCA main entry tests."""
    cmd = "python3 -m pytest tests/basic/test_wca_main_entry.py -v"
    return run_command(cmd, "Running WCA main entry tests")

def run_all_wca_tests():
    """Run all WCA tests."""
    cmd = "python3 -m pytest tests/basic/test_wca_*.py -v"
    return run_command(cmd, "Running all WCA tests")

def run_quick_wca_tests():
    """Run quick WCA tests."""
    cmd = "python3 tests/test_wca_suite.py --quick"
    return run_command(cmd, "Running quick WCA tests")

def run_comprehensive_wca_tests():
    """Run comprehensive WCA test suite."""
    cmd = "python3 tests/test_wca_suite.py"
    return run_command(cmd, "Running comprehensive WCA test suite")

def run_original_tests():
    """Run original aider tests."""
    cmd = "python3 -m pytest tests/basic/test_main.py -v"
    return run_command(cmd, "Running original aider tests")

def run_all_tests():
    """Run all tests (original + WCA)."""
    cmd = "python3 -m pytest tests/basic/ -v"
    return run_command(cmd, "Running all tests")

def check_test_environment():
    """Check if the test environment is properly set up."""
    print("🔍 Checking test environment...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} OK")
    
    # Check if pytest is available
    try:
        import pytest
        print(f"✅ pytest {pytest.__version__} available")
    except ImportError:
        print("❌ pytest not available - install with: pip install pytest")
        return False
    
    # Check if WCA Code modules can be imported
    sys.path.insert(0, '.')
    wca_modules = [
        ('wca_code.cli_simple', 'WCA Simple CLI'),
        ('wca_code.__main__', 'WCA Main Entry'),
    ]
    
    for module, description in wca_modules:
        try:
            __import__(module)
            print(f"✅ {description} available")
        except ImportError as e:
            print(f"⚠️  {description} not available: {e}")
    
    # Check optional dependencies
    optional_deps = [
        ('typer', 'Typer CLI framework'),
        ('rich', 'Rich terminal formatting'),
        ('yaml', 'YAML configuration support'),
    ]
    
    for dep, description in optional_deps:
        try:
            __import__(dep)
            print(f"✅ {description} available")
        except ImportError:
            print(f"⚠️  {description} not available (optional)")
    
    print("🎉 Environment check completed")
    return True

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Test runner for WCA Code",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 run_wca_tests.py --cli          # Run CLI tests
  python3 run_wca_tests.py --wellness     # Run wellness tests
  python3 run_wca_tests.py --all-wca      # Run all WCA tests
  python3 run_wca_tests.py --quick        # Run quick tests
  python3 run_wca_tests.py --check        # Check environment
        """
    )
    
    parser.add_argument("--cli", action="store_true", help="Run WCA CLI tests")
    parser.add_argument("--wellness", action="store_true", help="Run WCA wellness tests")
    parser.add_argument("--config", action="store_true", help="Run WCA config tests")
    parser.add_argument("--main-entry", action="store_true", help="Run WCA main entry tests")
    parser.add_argument("--all-wca", action="store_true", help="Run all WCA tests")
    parser.add_argument("--quick", action="store_true", help="Run quick WCA tests")
    parser.add_argument("--comprehensive", action="store_true", help="Run comprehensive WCA test suite")
    parser.add_argument("--original", action="store_true", help="Run original aider tests")
    parser.add_argument("--all", action="store_true", help="Run all tests")
    parser.add_argument("--check", action="store_true", help="Check test environment")
    
    args = parser.parse_args()
    
    # If no specific test type is specified, show help
    if not any(vars(args).values()):
        parser.print_help()
        return 0
    
    success = True
    
    if args.check:
        success &= check_test_environment()
    
    if args.cli:
        success &= run_wca_cli_tests()
    
    if args.wellness:
        success &= run_wca_wellness_tests()
    
    if args.config:
        success &= run_wca_config_tests()
    
    if args.main_entry:
        success &= run_wca_main_entry_tests()
    
    if args.all_wca:
        success &= run_all_wca_tests()
    
    if args.quick:
        success &= run_quick_wca_tests()
    
    if args.comprehensive:
        success &= run_comprehensive_wca_tests()
    
    if args.original:
        success &= run_original_tests()
    
    if args.all:
        success &= run_all_tests()
    
    if success:
        print("\n🎉 All requested tests completed successfully!")
        return 0
    else:
        print("\n❌ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
