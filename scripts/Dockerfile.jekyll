# Use the official Je<PERSON><PERSON> image from Docker Hub
FROM bretfisher/jekyll-serve

# Set the working directory
WORKDIR /site

# Copy the current directory contents into the container at /srv/jekyll
COPY website /site

RUN apt-get update && apt-get install libcurl4

# Install any needed packages specified in Gemfile
RUN bundle install --retry 5 --jobs 20

ENTRYPOINT [ "docker-entrypoint.sh" ]

# bundle exec jekyll serve --force_polling -H 0.0.0.0 -P 4000
CMD [ "bundle", "exec", "jekyll", "serve", "--verbose", "--trace", "--force_polling", "-H", "0.0.0.0", "-P", "4000" ]


