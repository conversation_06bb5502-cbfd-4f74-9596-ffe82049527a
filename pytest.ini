[pytest]
norecursedirs = tmp.* build benchmark _site OLD
addopts = -p no:warnings -v --tb=short --color=yes
testpaths =
    tests/basic
    tests/help
    tests/browser
    tests/scrape

env =
    AIDER_ANALYTICS=false
    WCA_CODE_ANALYTICS=false

# Markers for WCA Code tests
markers =
    wca_cli: marks tests as WCA CLI-related
    wca_wellness: marks tests as WCA wellness-related
    wca_config: marks tests as WCA configuration-related
    requires_typer: marks tests that require typer
    requires_rich: marks tests that require rich
    requires_git: marks tests that require git
    slow: marks tests as slow
    integration: marks tests as integration tests

# Test timeout (in seconds)
timeout = 300

# Ignore certain warnings for WCA Code
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:importlib_resources

