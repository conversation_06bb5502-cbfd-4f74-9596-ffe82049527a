"""
Comprehensive test suite for WCA Code functionality.
This module runs all WCA Code tests and provides a summary.
"""

import sys
import unittest
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import all test modules
try:
    from tests.basic.test_wca_cli import TestWCASimpleCLI, TestWCATyperCLI, TestWCAMainEntry, TestWCAIntegration
    WCA_CLI_TESTS = True
except ImportError as e:
    print(f"Warning: Could not import WCA CLI tests: {e}")
    WCA_CLI_TESTS = False

try:
    from tests.basic.test_wca_wellness import TestWellnessChecks, TestWellnessScoring, TestWellnessCommands, TestWellnessIntegration
    WCA_WELLNESS_TESTS = True
except ImportError as e:
    print(f"Warning: Could not import WCA wellness tests: {e}")
    WCA_WELLNESS_TESTS = False

try:
    from tests.basic.test_wca_config import TestWCAConfiguration, TestImportFixing, TestSetupScripts
    WCA_CONFIG_TESTS = True
except ImportError as e:
    print(f"Warning: Could not import WCA config tests: {e}")
    WCA_CONFIG_TESTS = False

try:
    from tests.basic.test_wca_main_entry import TestMainEntry, TestCLISelection, TestCLICompatibility, TestErrorRecovery
    WCA_MAIN_ENTRY_TESTS = True
except ImportError as e:
    print(f"Warning: Could not import WCA main entry tests: {e}")
    WCA_MAIN_ENTRY_TESTS = False


class WCATestSuite:
    """Comprehensive test suite for WCA Code."""
    
    def __init__(self):
        self.suite = unittest.TestSuite()
        self.test_counts = {
            'cli': 0,
            'wellness': 0,
            'config': 0,
            'main_entry': 0,
            'total': 0
        }
    
    def add_cli_tests(self):
        """Add CLI-related tests."""
        if not WCA_CLI_TESTS:
            print("Skipping CLI tests - modules not available")
            return
        
        cli_tests = [
            TestWCASimpleCLI,
            TestWCATyperCLI,
            TestWCAMainEntry,
            TestWCAIntegration,
        ]
        
        for test_class in cli_tests:
            tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
            self.suite.addTests(tests)
            self.test_counts['cli'] += tests.countTestCases()
    
    def add_wellness_tests(self):
        """Add wellness-related tests."""
        if not WCA_WELLNESS_TESTS:
            print("Skipping wellness tests - modules not available")
            return
        
        wellness_tests = [
            TestWellnessChecks,
            TestWellnessScoring,
            TestWellnessCommands,
            TestWellnessIntegration,
        ]
        
        for test_class in wellness_tests:
            tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
            self.suite.addTests(tests)
            self.test_counts['wellness'] += tests.countTestCases()
    
    def add_config_tests(self):
        """Add configuration-related tests."""
        if not WCA_CONFIG_TESTS:
            print("Skipping config tests - modules not available")
            return
        
        config_tests = [
            TestWCAConfiguration,
            TestImportFixing,
            TestSetupScripts,
        ]
        
        for test_class in config_tests:
            tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
            self.suite.addTests(tests)
            self.test_counts['config'] += tests.countTestCases()
    
    def add_main_entry_tests(self):
        """Add main entry point tests."""
        if not WCA_MAIN_ENTRY_TESTS:
            print("Skipping main entry tests - modules not available")
            return
        
        main_entry_tests = [
            TestMainEntry,
            TestCLISelection,
            TestCLICompatibility,
            TestErrorRecovery,
        ]
        
        for test_class in main_entry_tests:
            tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
            self.suite.addTests(tests)
            self.test_counts['main_entry'] += tests.countTestCases()
    
    def build_suite(self):
        """Build the complete test suite."""
        print("Building WCA Code test suite...")
        
        self.add_cli_tests()
        self.add_wellness_tests()
        self.add_config_tests()
        self.add_main_entry_tests()
        
        self.test_counts['total'] = self.suite.countTestCases()
        
        print(f"Test suite built with {self.test_counts['total']} tests:")
        print(f"  CLI tests: {self.test_counts['cli']}")
        print(f"  Wellness tests: {self.test_counts['wellness']}")
        print(f"  Config tests: {self.test_counts['config']}")
        print(f"  Main entry tests: {self.test_counts['main_entry']}")
        
        return self.suite
    
    def run_tests(self, verbosity=2):
        """Run all tests and return results."""
        suite = self.build_suite()
        
        if suite.countTestCases() == 0:
            print("No tests to run - WCA Code modules not available")
            return None
        
        print(f"\nRunning {suite.countTestCases()} WCA Code tests...\n")
        
        runner = unittest.TextTestRunner(verbosity=verbosity)
        result = runner.run(suite)
        
        return result
    
    def print_summary(self, result):
        """Print a summary of test results."""
        if result is None:
            print("No test results to summarize")
            return
        
        print("\n" + "="*60)
        print("WCA CODE TEST SUMMARY")
        print("="*60)
        
        print(f"Tests run: {result.testsRun}")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
        print(f"Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
        
        if result.failures:
            print(f"\nFAILURES ({len(result.failures)}):")
            for test, traceback in result.failures:
                print(f"  - {test}")
        
        if result.errors:
            print(f"\nERRORS ({len(result.errors)}):")
            for test, traceback in result.errors:
                print(f"  - {test}")
        
        success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0
        print(f"\nSuccess rate: {success_rate:.1f}%")
        
        if result.wasSuccessful():
            print("🎉 All tests passed!")
        else:
            print("❌ Some tests failed")
        
        print("="*60)


def run_wca_tests():
    """Main function to run WCA Code tests."""
    test_suite = WCATestSuite()
    result = test_suite.run_tests()
    test_suite.print_summary(result)
    
    return result.wasSuccessful() if result else False


def run_quick_tests():
    """Run a quick subset of tests for CI/development."""
    print("Running quick WCA Code tests...")
    
    # Run only the most important tests
    suite = unittest.TestSuite()
    
    if WCA_CLI_TESTS:
        # Add a few key CLI tests
        suite.addTest(TestWCASimpleCLI('test_version_command'))
        suite.addTest(TestWCASimpleCLI('test_models_command'))
        suite.addTest(TestWCASimpleCLI('test_config_init'))
    
    if WCA_CONFIG_TESTS:
        # Add key config tests
        suite.addTest(TestWCAConfiguration('test_wca_config_file_creation'))
        suite.addTest(TestImportFixing('test_fix_aider_imports'))
    
    if suite.countTestCases() == 0:
        print("No quick tests available - WCA Code modules not found")
        return False
    
    print(f"Running {suite.countTestCases()} quick tests...")
    
    runner = unittest.TextTestRunner(verbosity=1)
    result = runner.run(suite)
    
    print(f"\nQuick test results: {result.testsRun - len(result.failures) - len(result.errors)}/{result.testsRun} passed")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Run WCA Code tests")
    parser.add_argument("--quick", action="store_true", help="Run quick tests only")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    if args.quick:
        success = run_quick_tests()
    else:
        success = run_wca_tests()
    
    sys.exit(0 if success else 1)
