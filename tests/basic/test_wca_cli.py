"""
Test cases for WCA Code CLI functionality.
Tests the modern Typer-based CLI and simple fallback CLI.
"""

import os
import sys
import tempfile
from io import StringIO
from pathlib import Path
from unittest import TestCase
from unittest.mock import MagicMock, patch

import pytest

# Add the project root to the path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from wca_code.cli_simple import main as simple_main
    from wca_code.cli_simple import (
        handle_version,
        handle_models,
        handle_wellness_check,
        handle_config,
    )
    WCA_CODE_AVAILABLE = True
except ImportError:
    WCA_CODE_AVAILABLE = False

try:
    import typer
    from wca_code.cli import app as typer_app
    TYPER_AVAILABLE = True
except ImportError:
    TYPER_AVAILABLE = False


class TestWCASimpleCLI(TestCase):
    """Test the simple CLI that works without external dependencies."""

    def setUp(self):
        """Set up test environment."""
        self.original_cwd = os.getcwd()
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)

        # Mock sys.argv to prevent interference
        self.original_argv = sys.argv.copy()

    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        sys.argv = self.original_argv
        # Clean up temp directory
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @pytest.mark.skipif(not WCA_CODE_AVAILABLE, reason="WCA Code not available")
    def test_version_command(self):
        """Test the version command."""
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            handle_version()
            output = mock_stdout.getvalue()
            self.assertIn("WCA Code version", output)

    @pytest.mark.skipif(not WCA_CODE_AVAILABLE, reason="WCA Code not available")
    def test_models_command(self):
        """Test the models command."""
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            handle_models()
            output = mock_stdout.getvalue()
            self.assertIn("Available AI models", output)
            self.assertIn("gpt-4", output)
            self.assertIn("claude", output)

    @pytest.mark.skipif(not WCA_CODE_AVAILABLE, reason="WCA Code not available")
    def test_wellness_check_no_git(self):
        """Test wellness check in a non-git directory."""
        # Create a simple Python file
        test_file = Path("test.py")
        test_file.write_text("def hello():\n    return 'world'\n")

        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            # Create a mock args object
            class MockArgs:
                path = "."
                detailed = False

            result = handle_wellness_check(MockArgs())
            output = mock_stdout.getvalue()

            self.assertIn("Checking code wellness", output)
            self.assertIn("Python files", output)

    @pytest.mark.skipif(not WCA_CODE_AVAILABLE, reason="WCA Code not available")
    def test_config_show(self):
        """Test config show command."""
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            class MockArgs:
                show = True
                edit = False
                init = False

            handle_config(MockArgs())
            output = mock_stdout.getvalue()
            self.assertIn("Configuration files", output)

    @pytest.mark.skipif(not WCA_CODE_AVAILABLE, reason="WCA Code not available")
    def test_config_init(self):
        """Test config initialization."""
        config_file = Path(".wca_code.conf.yml")
        self.assertFalse(config_file.exists())

        with patch('builtins.input', return_value='y'):
            class MockArgs:
                show = False
                edit = False
                init = True

            handle_config(MockArgs())

        self.assertTrue(config_file.exists())
        content = config_file.read_text()
        self.assertIn("WCA Code Configuration", content)
        self.assertIn("model:", content)

    @pytest.mark.skipif(not WCA_CODE_AVAILABLE, reason="WCA Code not available")
    def test_simple_main_help(self):
        """Test the simple main function with help."""
        with patch('sys.argv', ['wca-code', '--help']):
            with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
                with self.assertRaises(SystemExit) as cm:
                    simple_main()

                # Help should exit with code 0
                self.assertEqual(cm.exception.code, 0)
                output = mock_stdout.getvalue()
                self.assertIn("WCA Code", output)
                self.assertIn("usage:", output.lower())

    @pytest.mark.skipif(not WCA_CODE_AVAILABLE, reason="WCA Code not available")
    def test_simple_main_version(self):
        """Test the simple main function with version command."""
        with patch('sys.argv', ['wca-code', 'version']):
            with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
                result = simple_main()

                # simple_main may return None, which is equivalent to 0
                self.assertIn(result, [0, None])
                output = mock_stdout.getvalue()
                self.assertIn("WCA Code version", output)

    @pytest.mark.skipif(not WCA_CODE_AVAILABLE, reason="WCA Code not available")
    def test_simple_main_models(self):
        """Test the simple main function with models command."""
        with patch('sys.argv', ['wca-code', 'models']):
            with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
                result = simple_main()

                # simple_main may return None, which is equivalent to 0
                self.assertIn(result, [0, None])
                output = mock_stdout.getvalue()
                self.assertIn("Available AI models", output)

    @pytest.mark.skipif(not WCA_CODE_AVAILABLE, reason="WCA Code not available")
    def test_simple_main_wellness_check(self):
        """Test the simple main function with wellness-check command."""
        # Create some test files
        Path("README.md").write_text("# Test Project\n")
        Path("test.py").write_text("print('hello')\n")

        with patch('sys.argv', ['wca-code', 'wellness-check']):
            with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
                result = simple_main()

                # Should return 1 because it's not a git repo
                self.assertEqual(result, 1)
                output = mock_stdout.getvalue()
                self.assertIn("Checking code wellness", output)

    @pytest.mark.skipif(not WCA_CODE_AVAILABLE, reason="WCA Code not available")
    def test_simple_main_no_args(self):
        """Test the simple main function with no arguments."""
        with patch('sys.argv', ['wca-code']):
            with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
                result = simple_main()

                self.assertEqual(result, 0)
                output = mock_stdout.getvalue()
                self.assertIn("WCA Code", output)
                self.assertIn("Quick start", output)


class TestWCATyperCLI(TestCase):
    """Test the Typer-based CLI (if available)."""

    @pytest.mark.skipif(not TYPER_AVAILABLE, reason="Typer not available")
    def test_typer_app_exists(self):
        """Test that the Typer app is properly configured."""
        self.assertIsInstance(typer_app, typer.Typer)
        self.assertEqual(typer_app.info.name, "wca-code")

    @pytest.mark.skipif(not TYPER_AVAILABLE, reason="Typer not available")
    def test_typer_commands_registered(self):
        """Test that commands are properly registered in the Typer app."""
        # Get the list of registered commands
        commands = list(typer_app.registered_commands.keys())

        # Check that key commands are registered
        expected_commands = ["version", "models", "chat", "lint", "test", "commit"]
        for cmd in expected_commands:
            self.assertIn(cmd, commands)


class TestWCAMainEntry(TestCase):
    """Test the main entry point and fallback system."""

    def setUp(self):
        """Set up test environment."""
        self.original_argv = sys.argv.copy()

    def tearDown(self):
        """Clean up test environment."""
        sys.argv = self.original_argv

    @pytest.mark.skipif(not WCA_CODE_AVAILABLE, reason="WCA Code not available")
    def test_main_entry_fallback(self):
        """Test that the main entry point falls back correctly."""
        # Test the __main__.py entry point
        with patch('sys.argv', ['wca-code', 'version']):
            with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
                try:
                    from wca_code.__main__ import main as main_entry
                    result = main_entry()

                    # Should work regardless of which CLI is used
                    output = mock_stdout.getvalue()
                    self.assertIn("WCA Code", output)
                except ImportError:
                    self.skipTest("WCA Code main entry not available")


class TestWCAIntegration(TestCase):
    """Integration tests for WCA Code functionality."""

    def setUp(self):
        """Set up test environment."""
        self.original_cwd = os.getcwd()
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)

    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @pytest.mark.skipif(not WCA_CODE_AVAILABLE, reason="WCA Code not available")
    def test_config_file_creation_and_reading(self):
        """Test creating and reading WCA Code config files."""
        config_content = """# WCA Code Configuration
model: gpt-4
auto-commits: true
stream: true
pretty: true
"""

        config_file = Path(".wca_code.conf.yml")
        config_file.write_text(config_content)

        self.assertTrue(config_file.exists())
        content = config_file.read_text()
        self.assertIn("WCA Code Configuration", content)
        self.assertIn("model: gpt-4", content)

    @pytest.mark.skipif(not WCA_CODE_AVAILABLE, reason="WCA Code not available")
    def test_wellness_check_with_python_files(self):
        """Test wellness check with various Python files."""
        # Create a sample Python project structure
        Path("src").mkdir()
        Path("tests").mkdir()

        # Create some Python files
        (Path("src") / "__init__.py").write_text("")
        (Path("src") / "main.py").write_text("""
def main():
    print("Hello, World!")

if __name__ == "__main__":
    main()
""")

        (Path("tests") / "test_main.py").write_text("""
import unittest
from src.main import main

class TestMain(unittest.TestCase):
    def test_main(self):
        # This would test the main function
        pass
""")

        Path("README.md").write_text("# Test Project")
        Path("requirements.txt").write_text("pytest>=6.0.0")

        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            class MockArgs:
                path = "."
                detailed = True

            handle_wellness_check(MockArgs())
            output = mock_stdout.getvalue()

            self.assertIn("Checking code wellness", output)
            self.assertIn("Python files", output)
            self.assertIn("README.md", output)
            self.assertIn("requirements.txt", output)


if __name__ == "__main__":
    # Run the tests
    import unittest
    unittest.main()
