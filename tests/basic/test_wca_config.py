"""
Test cases for WCA Code configuration and setup functionality.
Tests configuration management, import fixing, and setup scripts.
"""

import os
import sys
import tempfile
import yaml
from io import String<PERSON>
from pathlib import Path
from unittest import TestCase
from unittest.mock import patch, mock_open

import pytest

# Add the project root to the path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))


class TestWCAConfiguration(TestCase):
    """Test WCA Code configuration management."""

    def setUp(self):
        """Set up test environment."""
        self.original_cwd = os.getcwd()
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)

    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_wca_config_file_creation(self):
        """Test creating WCA Code configuration file."""
        config_content = """# WCA Code Configuration
model: gpt-4
auto-commits: true
stream: true
pretty: true
dark-mode: false

# Advanced settings
map-tokens: 1024
cache-prompts: false
"""
        
        config_file = Path(".wca_code.conf.yml")
        config_file.write_text(config_content)
        
        self.assertTrue(config_file.exists())
        
        # Parse and validate YAML
        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)
        
        self.assertEqual(config_data['model'], 'gpt-4')
        self.assertTrue(config_data['auto-commits'])
        self.assertTrue(config_data['stream'])
        self.assertFalse(config_data['dark-mode'])

    def test_config_file_search_order(self):
        """Test configuration file search order."""
        # Create config files in different locations
        home_config = Path.home() / ".wca_code.conf.yml"
        cwd_config = Path(".wca_code.conf.yml")
        
        # Create CWD config (should have highest priority)
        cwd_config.write_text("model: gpt-4-turbo\npriority: cwd")
        
        # Simulate home config (lower priority)
        home_content = "model: gpt-3.5-turbo\npriority: home"
        
        # Test that CWD config takes precedence
        with open(cwd_config, 'r') as f:
            config_data = yaml.safe_load(f)
        
        self.assertEqual(config_data['model'], 'gpt-4-turbo')
        self.assertEqual(config_data['priority'], 'cwd')

    def test_environment_variable_override(self):
        """Test that environment variables override config files."""
        # Create config file
        config_file = Path(".wca_code.conf.yml")
        config_file.write_text("model: gpt-4\nverbose: false")
        
        # Set environment variable
        os.environ["WCA_CODE_MODEL"] = "claude-3-sonnet"
        os.environ["WCA_CODE_VERBOSE"] = "true"
        
        try:
            # Environment variables should override config file
            self.assertEqual(os.environ.get("WCA_CODE_MODEL"), "claude-3-sonnet")
            self.assertEqual(os.environ.get("WCA_CODE_VERBOSE"), "true")
        finally:
            # Clean up environment variables
            os.environ.pop("WCA_CODE_MODEL", None)
            os.environ.pop("WCA_CODE_VERBOSE", None)

    def test_config_validation(self):
        """Test configuration validation."""
        # Test valid config
        valid_config = {
            'model': 'gpt-4',
            'auto-commits': True,
            'stream': True,
            'map-tokens': 1024
        }
        
        # All values should be of correct types
        self.assertIsInstance(valid_config['model'], str)
        self.assertIsInstance(valid_config['auto-commits'], bool)
        self.assertIsInstance(valid_config['stream'], bool)
        self.assertIsInstance(valid_config['map-tokens'], int)

    def test_config_file_comments_preserved(self):
        """Test that comments in config files are preserved."""
        config_content = """# WCA Code Configuration
# See https://wca-code.example.com/docs/config.html for all options

# Model settings
model: gpt-4  # Main model for coding
# weak-model: gpt-3.5-turbo  # Commented out

# Git settings
auto-commits: true  # Enable automatic commits
dirty-commits: true

# Output settings
dark-mode: false
stream: true
pretty: true
"""
        
        config_file = Path(".wca_code.conf.yml")
        config_file.write_text(config_content)
        
        # Read back the content
        content = config_file.read_text()
        
        # Check that comments are preserved
        self.assertIn("# WCA Code Configuration", content)
        self.assertIn("# Model settings", content)
        self.assertIn("# Main model for coding", content)
        self.assertIn("# weak-model: gpt-3.5-turbo  # Commented out", content)


class TestImportFixing(TestCase):
    """Test import fixing functionality."""

    def setUp(self):
        """Set up test environment."""
        self.original_cwd = os.getcwd()
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)
        
        # Create a mock wca_code directory
        self.wca_code_dir = Path("wca_code")
        self.wca_code_dir.mkdir()

    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_fix_aider_imports(self):
        """Test fixing imports from aider to relative imports."""
        # Create a test file with aider imports
        test_file = self.wca_code_dir / "test_module.py"
        original_content = """
from aider.models import Model
from aider.io import InputOutput
import aider.utils
from aider import __version__
"""
        test_file.write_text(original_content)
        
        # Expected content after fixing
        expected_patterns = [
            "from .models import Model",
            "from .io import InputOutput", 
            "from . import utils",
            "from . import __version__"
        ]
        
        # Simulate import fixing (we'll test the patterns)
        content = test_file.read_text()
        
        # Apply the same transformations as fix_imports.py
        import re
        patterns = [
            (r'from aider\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*) import', r'from .\1 import'),
            (r'from aider import', r'from . import'),
            (r'import aider\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)', r'from . import \1'),
        ]
        
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Write back the fixed content
        test_file.write_text(content)
        
        # Verify the fixes
        fixed_content = test_file.read_text()
        for expected in expected_patterns:
            self.assertIn(expected, fixed_content)

    def test_fix_resource_imports(self):
        """Test fixing resource imports."""
        test_file = self.wca_code_dir / "models.py"
        original_content = '''
import importlib.resources
with importlib.resources.open_text("aider.resources", "model-settings.yml") as f:
    data = f.read()
'''
        test_file.write_text(original_content)
        
        # Fix the import
        content = test_file.read_text()
        content = content.replace('"aider.resources"', '"wca_code.resources"')
        test_file.write_text(content)
        
        # Verify the fix
        fixed_content = test_file.read_text()
        self.assertIn('"wca_code.resources"', fixed_content)
        self.assertNotIn('"aider.resources"', fixed_content)

    def test_fix_config_file_references(self):
        """Test fixing configuration file references."""
        test_file = self.wca_code_dir / "args.py"
        original_content = '''
default=".aider.model.settings.yml"
help="Specify a file with aider model settings"
os.path.join(git_root, ".aider.input.history")
'''
        test_file.write_text(original_content)
        
        # Fix the references
        content = test_file.read_text()
        replacements = [
            ('.aider.model.settings.yml', '.wca_code.model.settings.yml'),
            ('.aider.input.history', '.wca_code.input.history'),
            ('aider model settings', 'wca_code model settings'),
        ]
        
        for old, new in replacements:
            content = content.replace(old, new)
        
        test_file.write_text(content)
        
        # Verify the fixes
        fixed_content = test_file.read_text()
        self.assertIn('.wca_code.model.settings.yml', fixed_content)
        self.assertIn('.wca_code.input.history', fixed_content)
        self.assertIn('wca_code model settings', fixed_content)

    def test_fix_cache_directories(self):
        """Test fixing cache directory references."""
        test_file = self.wca_code_dir / "analytics.py"
        original_content = '''
data_file = Path.home() / ".aider" / "analytics.json"
cache_dir = Path.home() / ".aider" / "caches"
'''
        test_file.write_text(original_content)
        
        # Fix the references
        content = test_file.read_text()
        content = content.replace('".aider"', '".wca_code"')
        test_file.write_text(content)
        
        # Verify the fixes
        fixed_content = test_file.read_text()
        self.assertIn('".wca_code"', fixed_content)
        self.assertNotIn('".aider"', fixed_content)


class TestSetupScripts(TestCase):
    """Test setup and installation scripts."""

    def setUp(self):
        """Set up test environment."""
        self.original_cwd = os.getcwd()
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)

    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_entry_script_creation(self):
        """Test creating the entry script."""
        script_content = '''#!/usr/bin/env python3
"""
WCA Code CLI entry script.
"""

import sys
from pathlib import Path

# Add the wca_code directory to Python path
wca_code_dir = Path(__file__).parent / "wca_code"
sys.path.insert(0, str(wca_code_dir))

from wca_code.cli_main import app

if __name__ == "__main__":
    app()
'''
        
        script_path = Path("wca-code")
        script_path.write_text(script_content)
        script_path.chmod(0o755)
        
        self.assertTrue(script_path.exists())
        self.assertTrue(os.access(script_path, os.X_OK))
        
        content = script_path.read_text()
        self.assertIn("WCA Code CLI entry script", content)
        self.assertIn("from wca_code.cli_main import app", content)

    def test_shell_completion_script(self):
        """Test creating shell completion script."""
        completion_script = '''#!/bin/bash
# WCA Code shell completion setup
# Add this to your ~/.bashrc or ~/.zshrc:
# source /path/to/this/script

_wca_code_completion() {
    local IFS=$'\\n'
    COMPREPLY=( $(env COMP_WORDS="${COMP_WORDS[*]}" \\
                     COMP_CWORD=$COMP_CWORD \\
                     _WCA_CODE_COMPLETE=complete $1) )
}

complete -F _wca_code_completion -o default wca-code
'''
        
        completion_path = Path("wca-code-completion.sh")
        completion_path.write_text(completion_script)
        
        self.assertTrue(completion_path.exists())
        
        content = completion_path.read_text()
        self.assertIn("WCA Code shell completion", content)
        self.assertIn("_wca_code_completion", content)
        self.assertIn("complete -F _wca_code_completion", content)

    @patch('subprocess.run')
    def test_dependency_installation_simulation(self, mock_run):
        """Test dependency installation (simulated)."""
        # Mock successful installation
        mock_run.return_value.returncode = 0
        
        # Simulate installing dependencies
        dependencies = [
            "typer[all]>=0.9.0",
            "rich>=13.0.0", 
            "click>=8.0.0",
        ]
        
        for dep in dependencies:
            # This would normally run pip install
            result = mock_run.return_value
            self.assertEqual(result.returncode, 0)

    def test_requirements_file_parsing(self):
        """Test parsing requirements.txt file."""
        requirements_content = """
# Core dependencies
typer[all]>=0.9.0
rich>=13.0.0
click>=8.0.0

# Optional dependencies
pytest>=6.0.0  # For testing
black>=22.0.0  # For code formatting

# Development dependencies
-e .  # Install in development mode
"""
        
        requirements_file = Path("requirements-cli.txt")
        requirements_file.write_text(requirements_content)
        
        # Parse the requirements
        lines = requirements_file.read_text().strip().split('\n')
        dependencies = []
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('-e'):
                # Remove inline comments
                dep = line.split('#')[0].strip()
                if dep:
                    dependencies.append(dep)
        
        expected_deps = [
            "typer[all]>=0.9.0",
            "rich>=13.0.0", 
            "click>=8.0.0",
            "pytest>=6.0.0",
            "black>=22.0.0"
        ]
        
        self.assertEqual(dependencies, expected_deps)


if __name__ == "__main__":
    import unittest
    unittest.main()
