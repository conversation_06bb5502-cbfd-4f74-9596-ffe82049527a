"""
Test cases for WCA Code wellness functionality.
Tests the code health checking, scoring, and wellness features.
"""

import os
import sys
import tempfile
import subprocess
from io import String<PERSON>
from pathlib import Path
from unittest import TestCase
from unittest.mock import MagicMock, patch

import pytest

# Add the project root to the path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from wca_code.cli_wellness import (
        wellness_app,
        wellness_check,
        wellness_score,
        check_git_health,
        check_code_quality,
        check_dependencies,
        check_test_coverage,
        calculate_git_score,
        calculate_quality_score,
        calculate_deps_score,
        calculate_test_score,
        get_status_emoji,
        display_wellness_report,
    )
    WELLNESS_AVAILABLE = True
except ImportError:
    WELLNESS_AVAILABLE = False


class TestWellnessChecks(TestCase):
    """Test individual wellness check functions."""

    def setUp(self):
        """Set up test environment."""
        self.original_cwd = os.getcwd()
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)

    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_check_git_health_no_git(self):
        """Test git health check in non-git directory."""
        result = check_git_health(Path("."))
        
        self.assertEqual(result["status"], "no_git")
        self.assertIn("Not a git repository", result["issues"])

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_check_git_health_with_git(self):
        """Test git health check in git directory."""
        # Initialize a git repository
        try:
            subprocess.run(["git", "init"], check=True, capture_output=True)
            subprocess.run(["git", "config", "user.name", "Test User"], check=True, capture_output=True)
            subprocess.run(["git", "config", "user.email", "<EMAIL>"], check=True, capture_output=True)
            
            result = check_git_health(Path("."))
            
            self.assertEqual(result["status"], "healthy")
            self.assertEqual(result["uncommitted_files"], 0)
            self.assertIsInstance(result["recent_commits"], int)
            
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.skipTest("Git not available or failed to initialize")

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_check_code_quality(self):
        """Test code quality check."""
        # Create some Python files
        Path("good_code.py").write_text("""
def hello_world():
    \"\"\"A simple hello world function.\"\"\"
    return "Hello, World!"

if __name__ == "__main__":
    print(hello_world())
""")
        
        Path("bad_code.py").write_text("""
def x():
    return 1+2+3+4+5+6+7+8+9+10+11+12+13+14+15
""")
        
        result = check_code_quality(Path("."))
        
        self.assertIn("status", result)
        self.assertIn("linting_errors", result)
        self.assertIn("complexity_issues", result)
        self.assertIn("issues", result)

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_check_dependencies(self):
        """Test dependency health check."""
        # Create a requirements.txt file
        Path("requirements.txt").write_text("""
pytest>=6.0.0
requests>=2.25.0
numpy>=1.20.0
""")
        
        result = check_dependencies(Path("."))
        
        self.assertIn("status", result)
        self.assertIn("outdated_packages", result)
        self.assertIn("security_issues", result)
        self.assertIn("issues", result)

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_check_test_coverage(self):
        """Test test coverage check."""
        # Create some test files
        Path("tests").mkdir(exist_ok=True)
        Path("tests/__init__.py").write_text("")
        Path("tests/test_example.py").write_text("""
import unittest

class TestExample(unittest.TestCase):
    def test_something(self):
        self.assertTrue(True)
""")
        
        result = check_test_coverage(Path("."))
        
        self.assertIn("status", result)
        self.assertIn("coverage_percentage", result)
        self.assertIn("missing_tests", result)
        self.assertIn("issues", result)


class TestWellnessScoring(TestCase):
    """Test wellness scoring functions."""

    def setUp(self):
        """Set up test environment."""
        self.original_cwd = os.getcwd()
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)

    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_calculate_git_score(self):
        """Test git score calculation."""
        score = calculate_git_score(Path("."))
        
        self.assertIsInstance(score, float)
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 10.0)

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_calculate_quality_score(self):
        """Test code quality score calculation."""
        score = calculate_quality_score(Path("."))
        
        self.assertIsInstance(score, float)
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 10.0)

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_calculate_deps_score(self):
        """Test dependencies score calculation."""
        score = calculate_deps_score(Path("."))
        
        self.assertIsInstance(score, float)
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 10.0)

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_calculate_test_score(self):
        """Test test coverage score calculation."""
        score = calculate_test_score(Path("."))
        
        self.assertIsInstance(score, float)
        self.assertGreaterEqual(score, 0.0)
        self.assertLessEqual(score, 10.0)

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_get_status_emoji(self):
        """Test status emoji generation."""
        self.assertIn("Excellent", get_status_emoji(9.5))
        self.assertIn("Good", get_status_emoji(8.0))
        self.assertIn("Fair", get_status_emoji(6.0))
        self.assertIn("Poor", get_status_emoji(3.0))


class TestWellnessCommands(TestCase):
    """Test wellness CLI commands."""

    def setUp(self):
        """Set up test environment."""
        self.original_cwd = os.getcwd()
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)

    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_wellness_check_command(self):
        """Test the wellness check command."""
        # Create a sample project
        Path("src").mkdir()
        Path("src/__init__.py").write_text("")
        Path("src/main.py").write_text("def main(): pass")
        Path("README.md").write_text("# Test Project")
        
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            with patch('rich.console.Console.print') as mock_print:
                # Mock the wellness check function
                wellness_check(path=Path("."), detailed=False)
                
                # Check that the function was called
                mock_print.assert_called()

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_wellness_score_command(self):
        """Test the wellness score command."""
        # Create a sample project
        Path("pyproject.toml").write_text("""
[tool.poetry]
name = "test-project"
version = "0.1.0"
""")
        
        with patch('sys.stdout', new_callable=StringIO) as mock_stdout:
            with patch('rich.console.Console.print') as mock_print:
                # Mock the wellness score function
                wellness_score(path=Path("."))
                
                # Check that the function was called
                mock_print.assert_called()

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_display_wellness_report(self):
        """Test wellness report display."""
        # Create mock health data
        git_health = {
            "status": "healthy",
            "uncommitted_files": 0,
            "recent_commits": 5,
            "issues": []
        }
        
        code_quality = {
            "status": "good",
            "linting_errors": 0,
            "complexity_issues": 1,
            "issues": ["High complexity in function X"]
        }
        
        deps_health = {
            "status": "good",
            "outdated_packages": 2,
            "security_issues": 0,
            "issues": []
        }
        
        test_health = {
            "status": "fair",
            "coverage_percentage": 75,
            "missing_tests": ["module_y.py"],
            "issues": ["Low coverage in module_y.py"]
        }
        
        with patch('rich.console.Console.print') as mock_print:
            display_wellness_report(git_health, code_quality, deps_health, test_health, detailed=True)
            
            # Check that the function was called
            mock_print.assert_called()


class TestWellnessIntegration(TestCase):
    """Integration tests for wellness functionality."""

    def setUp(self):
        """Set up test environment."""
        self.original_cwd = os.getcwd()
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)

    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @pytest.mark.skipif(not WELLNESS_AVAILABLE, reason="Wellness module not available")
    def test_full_wellness_workflow(self):
        """Test a complete wellness check workflow."""
        # Create a realistic project structure
        self._create_sample_project()
        
        # Run wellness checks
        git_health = check_git_health(Path("."))
        code_quality = check_code_quality(Path("."))
        deps_health = check_dependencies(Path("."))
        test_health = check_test_coverage(Path("."))
        
        # Verify all checks return expected structure
        for health_check in [git_health, code_quality, deps_health, test_health]:
            self.assertIn("status", health_check)
            self.assertIn("issues", health_check)
            self.assertIsInstance(health_check["issues"], list)
        
        # Calculate scores
        git_score = calculate_git_score(Path("."))
        quality_score = calculate_quality_score(Path("."))
        deps_score = calculate_deps_score(Path("."))
        test_score = calculate_test_score(Path("."))
        
        # Verify scores are in valid range
        for score in [git_score, quality_score, deps_score, test_score]:
            self.assertGreaterEqual(score, 0.0)
            self.assertLessEqual(score, 10.0)

    def _create_sample_project(self):
        """Create a sample project for testing."""
        # Create directory structure
        for directory in ["src", "tests", "docs"]:
            Path(directory).mkdir()
        
        # Create Python files
        Path("src/__init__.py").write_text("")
        Path("src/main.py").write_text("""
\"\"\"Main module for the application.\"\"\"

def main():
    \"\"\"Main entry point.\"\"\"
    print("Hello, World!")
    return 0

if __name__ == "__main__":
    main()
""")
        
        Path("tests/__init__.py").write_text("")
        Path("tests/test_main.py").write_text("""
\"\"\"Tests for the main module.\"\"\"
import unittest
from src.main import main

class TestMain(unittest.TestCase):
    \"\"\"Test cases for main function.\"\"\"
    
    def test_main_returns_zero(self):
        \"\"\"Test that main returns 0.\"\"\"
        result = main()
        self.assertEqual(result, 0)
""")
        
        # Create configuration files
        Path("README.md").write_text("# Sample Project\n\nThis is a test project.")
        Path("requirements.txt").write_text("pytest>=6.0.0\nrequests>=2.25.0\n")
        Path("pyproject.toml").write_text("""
[build-system]
requires = ["setuptools", "wheel"]

[project]
name = "sample-project"
version = "0.1.0"
description = "A sample project for testing"
""")
        
        Path(".gitignore").write_text("""
__pycache__/
*.pyc
.pytest_cache/
.coverage
""")


if __name__ == "__main__":
    import unittest
    unittest.main()
