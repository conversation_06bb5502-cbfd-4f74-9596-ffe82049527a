"""
Test cases for WCA Code Enhanced CLI with complete aider feature parity.
Tests all advanced features: voice, GUI, scraping, file watching, etc.
"""

import os
import sys
import tempfile
from io import StringIO
from pathlib import Path
from unittest import TestCase
from unittest.mock import MagicMock, patch, mock_open

import pytest

# Add the project root to the path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from wca_code.cli_enhanced import app as enhanced_app
    ENHANCED_CLI_AVAILABLE = True
except ImportError:
    ENHANCED_CLI_AVAILABLE = False

try:
    import typer
    from typer.testing import CliRunner
    TYPER_TESTING_AVAILABLE = True
except ImportError:
    TYPER_TESTING_AVAILABLE = False


class TestEnhancedCLI(TestCase):
    """Test the enhanced CLI with all advanced features."""

    def setUp(self):
        """Set up test environment."""
        self.original_cwd = os.getcwd()
        self.temp_dir = tempfile.mkdtemp()
        os.chdir(self.temp_dir)
        
        if TYPER_TESTING_AVAILABLE:
            self.runner = CliRunner()

    def tearDown(self):
        """Clean up test environment."""
        os.chdir(self.original_cwd)
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_version_command(self):
        """Test the enhanced version command."""
        result = self.runner.invoke(enhanced_app, ["version"])
        
        self.assertEqual(result.exit_code, 0)
        self.assertIn("WCA Code", result.stdout)

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_models_command(self):
        """Test the enhanced models command."""
        result = self.runner.invoke(enhanced_app, ["models"])
        
        self.assertEqual(result.exit_code, 0)
        self.assertIn("gpt-4", result.stdout)
        self.assertIn("claude", result.stdout)

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_models_with_provider_filter(self):
        """Test models command with provider filtering."""
        result = self.runner.invoke(enhanced_app, ["models", "--provider", "openai"])
        
        self.assertEqual(result.exit_code, 0)
        self.assertIn("gpt-4", result.stdout)

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_models_with_pricing(self):
        """Test models command with pricing information."""
        result = self.runner.invoke(enhanced_app, ["models", "--show-pricing"])
        
        self.assertEqual(result.exit_code, 0)
        self.assertIn("Pricing", result.stdout)

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_config_init(self):
        """Test configuration initialization."""
        # Mock user input to confirm overwrite
        with patch('builtins.input', return_value='y'):
            result = self.runner.invoke(enhanced_app, ["config", "--init"])
        
        self.assertEqual(result.exit_code, 0)
        
        # Check that config file was created
        config_file = Path(".wca_code.conf.yml")
        self.assertTrue(config_file.exists())
        
        content = config_file.read_text()
        self.assertIn("WCA Code Configuration", content)
        self.assertIn("model: gpt-4", content)

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_config_show(self):
        """Test configuration display."""
        # Create a config file first
        config_file = Path(".wca_code.conf.yml")
        config_file.write_text("""# WCA Code Configuration
model: gpt-4
stream: true
""")
        
        result = self.runner.invoke(enhanced_app, ["config", "--show"])
        
        self.assertEqual(result.exit_code, 0)
        self.assertIn("Configuration", result.stdout)

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_chat_command(self):
        """Test the enhanced chat command."""
        # Create a test file
        test_file = Path("test.py")
        test_file.write_text("def hello(): pass")
        
        # Mock the legacy_main function
        with patch('wca_code.cli_enhanced.legacy_main') as mock_main:
            mock_main.return_value = 0
            
            result = self.runner.invoke(enhanced_app, [
                "chat", 
                "test.py", 
                "--model", "gpt-4",
                "--message", "Fix this function"
            ])
            
            # Should call legacy_main with appropriate arguments
            mock_main.assert_called_once()
            args = mock_main.call_args[0][0]
            self.assertIn("test.py", args)
            self.assertIn("--model", args)
            self.assertIn("gpt-4", args)
            self.assertIn("--message", args)
            self.assertIn("Fix this function", args)

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_voice_command(self):
        """Test the voice command."""
        with patch('wca_code.cli_enhanced.Voice') as mock_voice:
            with patch('wca_code.cli_enhanced.legacy_main') as mock_main:
                mock_main.return_value = 0
                
                result = self.runner.invoke(enhanced_app, [
                    "voice",
                    "--language", "en",
                    "--audio-format", "wav"
                ])
                
                # Should attempt to create Voice instance
                mock_voice.assert_called_once()
                mock_main.assert_called_once()

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_scrape_command(self):
        """Test the web scraping command."""
        with patch('wca_code.cli_enhanced.Scraper') as mock_scraper:
            with patch('wca_code.cli_enhanced.has_playwright', return_value=True):
                # Mock scraper instance and content
                mock_scraper_instance = MagicMock()
                mock_scraper_instance.scrape.return_value = "# Test Content\n\nThis is scraped content."
                mock_scraper.return_value = mock_scraper_instance
                
                result = self.runner.invoke(enhanced_app, [
                    "scrape",
                    "https://example.com",
                    "--output", "scraped.md"
                ])
                
                self.assertEqual(result.exit_code, 0)
                
                # Check that scraper was called
                mock_scraper.assert_called_once()
                mock_scraper_instance.scrape.assert_called_once_with("https://example.com")
                
                # Check that output file was created
                output_file = Path("scraped.md")
                self.assertTrue(output_file.exists())
                content = output_file.read_text()
                self.assertIn("Test Content", content)

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_watch_command(self):
        """Test the file watching command."""
        with patch('wca_code.cli_enhanced.legacy_main') as mock_main:
            mock_main.return_value = 0
            
            result = self.runner.invoke(enhanced_app, [
                "watch",
                ".",
                "--model", "gpt-4",
                "--verbose"
            ])
            
            # Should call legacy_main with watch arguments
            mock_main.assert_called_once()
            args = mock_main.call_args[0][0]
            self.assertIn("--watch-files", args)
            self.assertIn("--model", args)
            self.assertIn("gpt-4", args)
            self.assertIn("--verbose", args)

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_lint_command(self):
        """Test the linting command."""
        # Create test files
        test_file = Path("test.py")
        test_file.write_text("def bad_function( ):\n  return 1+2")
        
        with patch('wca_code.cli_enhanced.legacy_main') as mock_main:
            mock_main.return_value = 0
            
            result = self.runner.invoke(enhanced_app, [
                "lint",
                "test.py",
                "--model", "gpt-4"
            ])
            
            # Should call legacy_main with lint arguments
            mock_main.assert_called_once()
            args = mock_main.call_args[0][0]
            self.assertIn("--lint", args)
            self.assertIn("test.py", args)

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_test_command(self):
        """Test the testing command."""
        # Create test files
        test_file = Path("test_example.py")
        test_file.write_text("""
import unittest

class TestExample(unittest.TestCase):
    def test_something(self):
        self.assertTrue(False)  # Failing test
""")
        
        with patch('wca_code.cli_enhanced.legacy_main') as mock_main:
            mock_main.return_value = 0
            
            result = self.runner.invoke(enhanced_app, [
                "test",
                "test_example.py",
                "--test-cmd", "python -m pytest"
            ])
            
            # Should call legacy_main with test arguments
            mock_main.assert_called_once()
            args = mock_main.call_args[0][0]
            self.assertIn("--test", args)
            self.assertIn("test_example.py", args)

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_commit_command(self):
        """Test the commit generation command."""
        with patch('wca_code.cli_enhanced.legacy_main') as mock_main:
            mock_main.return_value = 0
            
            result = self.runner.invoke(enhanced_app, [
                "commit",
                "--message", "Generate commit for recent changes"
            ])
            
            # Should call legacy_main with commit arguments
            mock_main.assert_called_once()
            args = mock_main.call_args[0][0]
            self.assertIn("--commit", args)
            self.assertIn("--commit-prompt", args)

    @pytest.mark.skipif(not ENHANCED_CLI_AVAILABLE, reason="Enhanced CLI not available")
    @pytest.mark.skipif(not TYPER_TESTING_AVAILABLE, reason="Typer testing not available")
    def test_enhanced_gui_command(self):
        """Test the GUI launch command."""
        with patch('wca_code.cli_enhanced.gui_main') as mock_gui:
            with patch.dict(os.environ, {}, clear=True):
                result = self.runner.invoke(enhanced_app, [
                    "gui",
                    "--port", "8502",
                    "--no-browser"
                ])
                
                # Should set environment variables and call gui_main
                self.assertEqual(os.environ.get("STREAMLIT_SERVER_PORT"), "8502")
                self.assertEqual(os.environ.get("STREAMLIT_SERVER_HEADLESS"), "true")
                mock_gui.assert_called_once()


class TestEnhancedFeatureDetection(TestCase):
    """Test feature detection and graceful degradation."""

    def test_feature_availability_detection(self):
        """Test that feature availability is properly detected."""
        # Test that the module can be imported even with missing dependencies
        try:
            from wca_code import cli_enhanced
            # Should not raise ImportError even if some features are unavailable
            self.assertTrue(hasattr(cli_enhanced, 'app'))
        except ImportError:
            # This is acceptable if the entire module is unavailable
            pass

    def test_graceful_degradation(self):
        """Test that commands degrade gracefully when dependencies are missing."""
        # This would test the fallback behavior when optional dependencies are missing
        # For now, we just verify the structure exists
        try:
            from wca_code.cli_enhanced import RICH_AVAILABLE
            # Should be a boolean indicating Rich availability
            self.assertIsInstance(RICH_AVAILABLE, bool)
        except ImportError:
            # Module not available, which is acceptable
            pass


if __name__ == "__main__":
    import unittest
    unittest.main()
