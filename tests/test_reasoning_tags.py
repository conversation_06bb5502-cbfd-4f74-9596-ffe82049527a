"""
Tests for the reasoning tags functionality.
Tests both ReasoningFormatting and ReasoningTag classes.
"""

import pytest
from wca_code.reasoning_tags import ReasoningFormatting, ReasoningTag


class TestReasoningFormatting:
    """Test suite for ReasoningFormatting class."""

    def test_default_formatting(self):
        """Test default formatting values."""
        formatting = ReasoningFormatting()
        assert "**THINKING**" in formatting.start_marker
        assert "**ANSWER**" in formatting.end_marker

    def test_custom_formatting(self):
        """Test custom formatting values."""
        custom_start = "==== START ===="
        custom_end = "==== END ===="
        formatting = ReasoningFormatting(
            start_marker=custom_start,
            end_marker=custom_end
        )
        assert formatting.start_marker == custom_start
        assert formatting.end_marker == custom_end


class TestReasoningTag:
    """Test suite for ReasoningTag class."""

    @pytest.fixture
    def default_tag(self):
        """Fixture providing default ReasoningTag instance."""
        return ReasoningTag()

    @pytest.fixture
    def custom_tag(self):
        """Fixture providing ReasoningTag with custom formatting."""
        formatting = ReasoningFormatting(
            start_marker="START",
            end_marker="END"
        )
        return ReasoningTag(tag_name="custom-tag", formatting=formatting)

    def test_default_tag_name(self, default_tag):
        """Test default tag name."""
        assert "thinking-content-" in default_tag.tag_name
        assert len(default_tag.tag_name) > 20  # Should include hash

    def test_custom_tag_name(self):
        """Test custom tag name."""
        custom_name = "my-special-tag"
        tag = ReasoningTag(tag_name=custom_name)
        assert tag.tag_name == custom_name

    def test_remove_content_empty_tag(self, default_tag):
        """Test removing content with empty tag name."""
        tag = ReasoningTag(tag_name="")
        text = "Some text with <tag>content</tag>"
        assert tag.remove_content(text) == text

    def test_remove_content_complete_tags(self, default_tag):
        """Test removing content with complete tags."""
        text = f"Before <{default_tag.tag_name}>remove this</{default_tag.tag_name}> After"
        result = default_tag.remove_content(text)
        assert result == "Before After"

    def test_remove_content_missing_opening_tag(self, default_tag):
        """Test removing content with missing opening tag."""
        text = f"Remove this</{default_tag.tag_name}> Keep this"
        result = default_tag.remove_content(text)
        assert result == "Keep this"

    def test_replace_tags_empty_text(self, default_tag):
        """Test replacing tags in empty text."""
        assert default_tag.replace_tags("") == ""

    def test_replace_tags_with_content(self, custom_tag):
        """Test replacing tags with custom formatting."""
        text = f"<{custom_tag.tag_name}>Content</{custom_tag.tag_name}>"
        result = custom_tag.replace_tags(text)
        assert "START" in result
        assert "END" in result
        assert "Content" in result

    def test_format_content_empty(self, default_tag):
        """Test formatting empty content."""
        assert default_tag.format_content("") == ""

    def test_format_content(self, default_tag):
        """Test formatting content with tags."""
        content = "Test content"
        result = default_tag.format_content(content)
        assert content in result
        assert result.startswith(f"<{default_tag.tag_name}>")
        assert result.endswith(f"</{default_tag.tag_name}>")

    def test_format_and_remove_roundtrip(self, default_tag):
        """Test that formatting and then removing tags preserves original content."""
        original = "Original content"
        formatted = default_tag.format_content(original)
        result = default_tag.remove_content(formatted)
        assert result == "Original content"

    def test_replace_tags_preserves_whitespace(self, custom_tag):
        """Test that replacing tags handles whitespace correctly."""
        text = (
            f"Before\n<{custom_tag.tag_name}>\nContent\n"
            f"</{custom_tag.tag_name}>\nAfter"
        )
        result = custom_tag.replace_tags(text)
        assert "Before" in result
        assert "Content" in result
        assert "After" in result
        assert result.count("\n\n") >= 2  # Should have blank lines around markers
