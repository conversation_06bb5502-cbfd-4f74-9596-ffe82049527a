# This file was autogenerated by uv via the following command:
#    uv pip compile --no-strip-extras --constraint=requirements/common-constraints.txt --output-file=requirements/requirements-dev.txt requirements/requirements-dev.in
build==1.2.2.post1
    # via
    #   -c requirements/common-constraints.txt
    #   pip-tools
cachetools==5.5.2
    # via
    #   -c requirements/common-constraints.txt
    #   google-auth
certifi==2025.4.26
    # via
    #   -c requirements/common-constraints.txt
    #   requests
cfgv==3.4.0
    # via
    #   -c requirements/common-constraints.txt
    #   pre-commit
charset-normalizer==3.4.2
    # via
    #   -c requirements/common-constraints.txt
    #   requests
click==8.1.8
    # via
    #   -c requirements/common-constraints.txt
    #   pip-tools
    #   typer
codespell==2.4.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
cogapp==3.4.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
contourpy==1.3.2
    # via
    #   -c requirements/common-constraints.txt
    #   matplotlib
cycler==0.12.1
    # via
    #   -c requirements/common-constraints.txt
    #   matplotlib
dill==0.4.0
    # via
    #   -c requirements/common-constraints.txt
    #   multiprocess
    #   pathos
distlib==0.3.9
    # via
    #   -c requirements/common-constraints.txt
    #   virtualenv
filelock==3.18.0
    # via
    #   -c requirements/common-constraints.txt
    #   virtualenv
fonttools==4.57.0
    # via
    #   -c requirements/common-constraints.txt
    #   matplotlib
google-api-core[grpc]==2.24.2
    # via
    #   -c requirements/common-constraints.txt
    #   google-cloud-bigquery
    #   google-cloud-core
google-auth==2.40.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
    #   google-cloud-bigquery
    #   google-cloud-core
google-cloud-bigquery==3.31.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
google-cloud-core==2.4.3
    # via
    #   -c requirements/common-constraints.txt
    #   google-cloud-bigquery
google-crc32c==1.7.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-resumable-media
google-resumable-media==2.7.2
    # via
    #   -c requirements/common-constraints.txt
    #   google-cloud-bigquery
googleapis-common-protos==1.70.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
    #   grpcio-status
grpcio==1.71.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
    #   grpcio-status
grpcio-status==1.71.0
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
identify==2.6.10
    # via
    #   -c requirements/common-constraints.txt
    #   pre-commit
idna==3.10
    # via
    #   -c requirements/common-constraints.txt
    #   requests
imgcat==0.6.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
iniconfig==2.1.0
    # via
    #   -c requirements/common-constraints.txt
    #   pytest
kiwisolver==1.4.8
    # via
    #   -c requirements/common-constraints.txt
    #   matplotlib
lox==0.13.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
markdown-it-py==3.0.0
    # via
    #   -c requirements/common-constraints.txt
    #   rich
matplotlib==3.10.3
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
mdurl==0.1.2
    # via
    #   -c requirements/common-constraints.txt
    #   markdown-it-py
multiprocess==0.70.18
    # via
    #   -c requirements/common-constraints.txt
    #   pathos
nodeenv==1.9.1
    # via
    #   -c requirements/common-constraints.txt
    #   pre-commit
numpy==1.26.4
    # via
    #   -c requirements/common-constraints.txt
    #   contourpy
    #   matplotlib
    #   pandas
packaging==24.2
    # via
    #   -c requirements/common-constraints.txt
    #   build
    #   google-cloud-bigquery
    #   matplotlib
    #   pytest
pandas==2.2.3
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
pathos==0.3.4
    # via
    #   -c requirements/common-constraints.txt
    #   lox
pillow==11.2.1
    # via
    #   -c requirements/common-constraints.txt
    #   matplotlib
pip==25.1.1
    # via
    #   -c requirements/common-constraints.txt
    #   pip-tools
pip-tools==7.4.1
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
platformdirs==4.3.8
    # via
    #   -c requirements/common-constraints.txt
    #   virtualenv
pluggy==1.5.0
    # via
    #   -c requirements/common-constraints.txt
    #   pytest
pox==0.3.6
    # via
    #   -c requirements/common-constraints.txt
    #   pathos
ppft==1.7.7
    # via
    #   -c requirements/common-constraints.txt
    #   pathos
pre-commit==4.2.0
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
proto-plus==1.26.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
protobuf==5.29.4
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
pyasn1==0.6.1
    # via
    #   -c requirements/common-constraints.txt
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via
    #   -c requirements/common-constraints.txt
    #   google-auth
pygments==2.19.1
    # via
    #   -c requirements/common-constraints.txt
    #   rich
pyparsing==3.2.3
    # via
    #   -c requirements/common-constraints.txt
    #   matplotlib
pyproject-hooks==1.2.0
    # via
    #   -c requirements/common-constraints.txt
    #   build
    #   pip-tools
pytest==8.3.5
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
    #   pytest-env
pytest-env==1.1.5
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
python-dateutil==2.9.0.post0
    # via
    #   -c requirements/common-constraints.txt
    #   google-cloud-bigquery
    #   matplotlib
    #   pandas
pytz==2025.2
    # via
    #   -c requirements/common-constraints.txt
    #   pandas
pyyaml==6.0.2
    # via
    #   -c requirements/common-constraints.txt
    #   pre-commit
requests==2.32.3
    # via
    #   -c requirements/common-constraints.txt
    #   google-api-core
    #   google-cloud-bigquery
rich==14.0.0
    # via
    #   -c requirements/common-constraints.txt
    #   typer
rsa==4.9.1
    # via
    #   -c requirements/common-constraints.txt
    #   google-auth
semver==3.0.4
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
setuptools==80.3.1
    # via
    #   -c requirements/common-constraints.txt
    #   pip-tools
shellingham==1.5.4
    # via
    #   -c requirements/common-constraints.txt
    #   typer
six==1.17.0
    # via
    #   -c requirements/common-constraints.txt
    #   python-dateutil
typer==0.15.3
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
typing-extensions==4.13.2
    # via
    #   -c requirements/common-constraints.txt
    #   typer
tzdata==2025.2
    # via
    #   -c requirements/common-constraints.txt
    #   pandas
urllib3==2.4.0
    # via
    #   -c requirements/common-constraints.txt
    #   requests
uv==0.7.3
    # via
    #   -c requirements/common-constraints.txt
    #   -r requirements/requirements-dev.in
virtualenv==20.31.2
    # via
    #   -c requirements/common-constraints.txt
    #   pre-commit
wheel==0.45.1
    # via
    #   -c requirements/common-constraints.txt
    #   pip-tools
