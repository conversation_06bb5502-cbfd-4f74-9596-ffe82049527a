#!/usr/bin/env python3
"""
Verification script to ensure complete migration from aider to wca_code.
This script verifies that all files have been migrated and imports fixed.
"""

import os
import sys
from pathlib import Path
import subprocess

def check_file_migration():
    """Check that all aider files have been migrated to wca_code."""
    print("🔍 Checking file migration...")

    aider_files = set()
    wca_files = set()

    # Get all Python files from aider
    for root, dirs, files in os.walk("aider"):
        for file in files:
            if file.endswith(".py"):
                rel_path = os.path.relpath(os.path.join(root, file), "aider")
                aider_files.add(rel_path)

    # Get all Python files from wca_code (excluding our new CLI files)
    for root, dirs, files in os.walk("wca_code"):
        for file in files:
            if file.endswith(".py"):
                rel_path = os.path.relpath(os.path.join(root, file), "wca_code")
                # Skip our new WCA-specific files
                if not any(rel_path.startswith(prefix) for prefix in [
                    "cli_", "wellness", "wca_code.py"
                ]):
                    wca_files.add(rel_path)

    missing_files = aider_files - wca_files
    extra_files = wca_files - aider_files

    if missing_files:
        print(f"❌ Missing files in wca_code: {missing_files}")
        return False

    if extra_files:
        print(f"ℹ️  Extra files in wca_code (expected): {extra_files}")

    print(f"✅ All {len(aider_files)} aider files migrated successfully")
    return True

def check_import_fixes():
    """Check that all aider imports have been fixed."""
    print("\n🔍 Checking import fixes...")

    # Check for remaining aider imports in Python files
    result = subprocess.run([
        "grep", "-r", "from aider", "wca_code/", "--include=*.py"
    ], capture_output=True, text=True)

    if result.returncode == 0:
        print("❌ Found remaining aider imports:")
        print(result.stdout)
        return False

    result = subprocess.run([
        "grep", "-r", "import aider", "wca_code/", "--include=*.py"
    ], capture_output=True, text=True)

    if result.returncode == 0:
        print("❌ Found remaining aider imports:")
        print(result.stdout)
        return False

    print("✅ All aider imports have been fixed")
    return True

def check_functionality():
    """Check that basic WCA Code functionality works."""
    print("\n🔍 Checking WCA Code functionality...")

    try:
        # Check that key files exist and are readable
        key_files = [
            "wca_code/__init__.py",
            "wca_code/__main__.py",
            "wca_code/main.py",
            "wca_code/cli_simple.py",
            "wca_code/models.py",
            "wca_code/io.py",
        ]

        for file_path in key_files:
            if not Path(file_path).exists():
                print(f"❌ Missing key file: {file_path}")
                return False

            # Try to read the file to ensure it's not corrupted
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if len(content) < 10:  # Basic sanity check
                        print(f"❌ File appears empty or corrupted: {file_path}")
                        return False
            except Exception as e:
                print(f"❌ Cannot read file {file_path}: {e}")
                return False

        print("✅ All key files exist and are readable")

        # Check that __init__.py has version
        init_content = Path("wca_code/__init__.py").read_text()
        if "__version__" not in init_content:
            print("❌ __version__ not found in __init__.py")
            return False

        print("✅ Version definition found in __init__.py")

        return True

    except Exception as e:
        print(f"❌ Functionality check failed: {e}")
        return False

def check_resources():
    """Check that resources have been migrated."""
    print("\n🔍 Checking resource migration...")

    aider_resources = Path("aider/resources")
    wca_resources = Path("wca_code/resources")

    if not wca_resources.exists():
        print("❌ WCA Code resources directory missing")
        return False

    # Check key resource files
    key_files = ["model-metadata.json", "model-settings.yml"]
    for file in key_files:
        aider_file = aider_resources / file
        wca_file = wca_resources / file

        if aider_file.exists() and not wca_file.exists():
            print(f"❌ Missing resource file: {file}")
            return False
        elif wca_file.exists():
            print(f"✅ Resource file migrated: {file}")

    return True

def check_queries():
    """Check that tree-sitter queries have been migrated."""
    print("\n🔍 Checking tree-sitter queries migration...")

    aider_queries = Path("aider/queries")
    wca_queries = Path("wca_code/queries")

    if not wca_queries.exists():
        print("❌ WCA Code queries directory missing")
        return False

    # Count query files
    aider_count = len(list(aider_queries.rglob("*.scm")))
    wca_count = len(list(wca_queries.rglob("*.scm")))

    if aider_count != wca_count:
        print(f"❌ Query file count mismatch: aider={aider_count}, wca_code={wca_count}")
        return False

    print(f"✅ All {wca_count} query files migrated successfully")
    return True

def check_website():
    """Check that website has been migrated."""
    print("\n🔍 Checking website migration...")

    aider_website = Path("aider/website")
    wca_website = Path("wca_code/website")

    if not wca_website.exists():
        print("❌ WCA Code website directory missing")
        return False

    # Check key website files
    key_files = ["_config.yml", "index.html", "Gemfile"]
    for file in key_files:
        aider_file = aider_website / file
        wca_file = wca_website / file

        if aider_file.exists() and not wca_file.exists():
            print(f"❌ Missing website file: {file}")
            return False
        elif wca_file.exists():
            print(f"✅ Website file migrated: {file}")

    return True

def generate_migration_report():
    """Generate a comprehensive migration report."""
    print("\n📊 MIGRATION REPORT")
    print("=" * 50)

    checks = [
        ("File Migration", check_file_migration),
        ("Import Fixes", check_import_fixes),
        ("Basic Functionality", check_functionality),
        ("Resources", check_resources),
        ("Tree-sitter Queries", check_queries),
        ("Website", check_website),
    ]

    results = {}
    all_passed = True

    for name, check_func in checks:
        try:
            results[name] = check_func()
            all_passed = all_passed and results[name]
        except Exception as e:
            print(f"❌ {name} check failed with error: {e}")
            results[name] = False
            all_passed = False

    print("\n📋 SUMMARY")
    print("-" * 30)
    for name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{name}: {status}")

    print(f"\n🎯 OVERALL: {'✅ MIGRATION COMPLETE' if all_passed else '❌ MIGRATION INCOMPLETE'}")

    if all_passed:
        print("\n🎉 All checks passed! The aider folder can be safely removed.")
        print("   WCA Code is fully functional and independent.")
    else:
        print("\n⚠️  Some checks failed. Please review and fix issues before removing aider folder.")

    return all_passed

def main():
    """Main function."""
    print("🚀 WCA Code Migration Verification")
    print("=" * 40)

    if not Path("aider").exists():
        print("❌ aider directory not found")
        return 1

    if not Path("wca_code").exists():
        print("❌ wca_code directory not found")
        return 1

    success = generate_migration_report()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
