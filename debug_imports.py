#!/usr/bin/env python3
"""
Debug script to identify problematic imports in wca_code.
"""

import sys
import signal
from pathlib import Path

def timeout_handler(signum, frame):
    print(f"❌ Import timed out at: {current_import}")
    sys.exit(1)

def test_import(module_name, description):
    global current_import
    current_import = f"{module_name} ({description})"
    
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(3)  # 3 second timeout per import
    
    try:
        if module_name == "wca_code":
            import wca_code
        elif module_name == "wca_code.__version__":
            from wca_code import __version__
        elif module_name == "wca_code.cli_simple":
            from wca_code.cli_simple import main
        elif module_name == "wca_code.main":
            from wca_code.main import main
        elif module_name == "wca_code.models":
            from wca_code import models
        elif module_name == "wca_code.io":
            from wca_code import io
        elif module_name == "wca_code.args":
            from wca_code import args
        elif module_name == "wca_code.analytics":
            from wca_code import analytics
        elif module_name == "wca_code.coders":
            from wca_code import coders
        else:
            exec(f"import {module_name}")
        
        signal.alarm(0)  # Cancel timeout
        print(f"✅ {description}")
        return True
        
    except Exception as e:
        signal.alarm(0)
        print(f"❌ {description}: {e}")
        return False

def main():
    """Test imports progressively."""
    print("🔍 Testing wca_code imports progressively...")
    
    # Add wca_code to path
    sys.path.insert(0, '.')
    
    # Test basic Python modules first
    basic_tests = [
        ("sys", "Basic sys module"),
        ("os", "Basic os module"),
        ("pathlib", "Basic pathlib module"),
    ]
    
    for module, desc in basic_tests:
        if not test_import(module, desc):
            return 1
    
    # Test wca_code modules progressively
    wca_tests = [
        ("wca_code", "Main wca_code module"),
        ("wca_code.__version__", "Version import"),
        ("wca_code.cli_simple", "Simple CLI module"),
        ("wca_code.args", "Args module"),
        ("wca_code.analytics", "Analytics module"),
        ("wca_code.io", "IO module"),
        ("wca_code.models", "Models module"),
        ("wca_code.coders", "Coders module"),
        ("wca_code.main", "Main module"),
    ]
    
    for module, desc in wca_tests:
        if not test_import(module, desc):
            print(f"\n💡 The issue is likely in: {module}")
            return 1
    
    print("\n🎉 All imports successful!")
    return 0

if __name__ == "__main__":
    current_import = "none"
    sys.exit(main())
