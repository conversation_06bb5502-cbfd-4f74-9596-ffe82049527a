name: Docker Release

on:
  workflow_dispatch:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'

jobs:
  docker_build_and_push:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to DockerHub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_PASSWORD }}

    - name: Build and push Docker images
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./docker/Dockerfile
        platforms: linux/amd64,linux/arm64
        push: true
        tags: |
          ${{ secrets.DOCKERHUB_USERNAME }}/aider:${{ github.ref_name }}
          ${{ secrets.DOCKERHUB_USERNAME }}/aider:latest
        target: aider

    - name: Build and push Docker full image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./docker/Dockerfile
        platforms: linux/amd64,linux/arm64
        push: true
        tags: |
          ${{ secrets.DOCKERHUB_USERNAME }}/aider-full:${{ github.ref_name }}
          ${{ secrets.DOCKERHUB_USERNAME }}/aider-full:latest
        target: aider-full
